<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form
          :model="queryParams"
          ref="queryForm"
          class="form-line"
          v-show="showSearch"
          label-width="120px">
        <el-row style="display: flex; flex-wrap: wrap;" :gutter="10">
          <el-col :span="6">
            <el-form-item label="所在单位" prop="orgValue" label-width="100px">
              <new-org-select
                  ref="newOrgSelect"
                  style="width: 100%;"
                  @handleOrgChange="handleOrgChange"
                  :currentOrgValue="queryParams.organizationNo"
                  :showLevel=3
                  @firstTrip="firstTripFun"
              ></new-org-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="模块名称" prop="gradecode">
              <el-cascader
                  :key="refcraft"
                  placeholder="请选择模块名称"
                  :show-all-levels="false"
                  :props="{label:'name',value:'gradecode',checkStrictly: true}"
                  v-model="queryParams.gradecode"
                  :options="billTypeoptions"
                  style="width: 100%;"
              ></el-cascader>
              <!--            <el-select-->
              <!--              v-model="queryParams.billType"-->
              <!--              placeholder="请选择模块名称"-->
              <!--              clearable-->
              <!--              size="small"-->
              <!--            >-->
              <!--              <el-option-->
              <!--                v-for="dict in billTypeNoOptions"-->
              <!--                :key="dict.code"-->
              <!--                :label="dict.name"-->
              <!--                :value="dict.code"-->
              <!--              />-->
              <!--            </el-select>-->
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="一级审核角色" prop="auditARoleId">
              <el-select
                  v-model="queryParams.auditARoleId"
                  placeholder="请选择一级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}1F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="二级审核角色" prop="auditBRoleId">
              <el-select
                  v-model="queryParams.auditBRoleId"
                  placeholder="请选择二级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}2F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="三级审核角色" prop="auditCRoleId" label-width="100px">
              <el-select
                  v-model="queryParams.auditCRoleId"
                  placeholder="请选择三级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}3F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="四级审核角色" prop="auditDRoleId">
              <el-select
                  v-model="queryParams.auditDRoleId"
                  placeholder="请选择四级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}4F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="五级审核角色" prop="auditERoleId">
              <el-select
                  v-model="queryParams.auditERoleId"
                  placeholder="请选择五级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}5F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="六级审核角色" prop="auditFRoleId">
              <el-select
                  v-model="queryParams.auditFRoleId"
                  placeholder="请选择六级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}6F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="七级审核角色" prop="auditGRoleId" label-width="100px">
              <el-select
                  v-model="queryParams.auditGRoleId"
                  placeholder="请选择七级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}7F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="八级审核角色" prop="auditHRoleId">
              <el-select
                  v-model="queryParams.auditHRoleId"
                  placeholder="请选择八级审核角色"
                  clearable
                  style="width: 100%;"
              >
                <el-option
                    v-for="(dict, index) in auditDeptNoFormat"
                    :key="`${dict.roleId}${index}8F`"
                    :label="dict.roleName"
                    :value="dict.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!--        <el-col :span="8">-->
          <!--          <el-form-item label="一级审核人姓名" prop="auditAName">-->
          <!--            <el-input-->
          <!--              v-model="queryParams.auditAName"-->
          <!--              placeholder="请输入一级审核人姓名"-->
          <!--              clearable-->
          <!--              size="small"-->
          <!--              @keyup.enter.native="handleQuery"-->
          <!--            />-->
          <!--          </el-form-item>-->
          <!--        </el-col>-->
          <!--      </el-row>-->
          <!--      <el-row>-->
          <!--        <el-col :span="18">-->
          <!--          <el-form-item label="二级审核人姓名" prop="auditBName">-->
          <!--            <el-input-->
          <!--              v-model="queryParams.auditBName"-->
          <!--              placeholder="请输入二级审核人姓名"-->
          <!--              clearable-->
          <!--              size="small"-->
          <!--              @keyup.enter.native="handleQuery"-->
          <!--            />-->
          <!--          </el-form-item>-->
          <!--        </el-col>-->
          <!--        <el-col :span="6">-->
          <!--          <el-form-item style="margin-left: 120px">-->
          <!--            <el-button-->
          <!--              type="primary"-->
          <!--              icon="el-icon-search"-->
          <!--              size="mini"-->
          <!--              @click="handleQuery"-->
          <!--            >搜索</el-button-->
          <!--            >-->
          <!--            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"-->
          <!--            >重置</el-button-->
          <!--            >-->
          <!--          </el-form-item>-->
          <!--        </el-col>-->
          <el-col :span="12" style="text-align: right;">
            <el-button  icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button  type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>


    <el-row :gutter="10" class="mb8" style="margin-bottom: 16px;" >
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['rationworkflow:insert']"
        >新建</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['rationworkflow:update']"-->
      <!--        >修改</el-button-->
      <!--        >-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['configWorkflow:logicDeleteByIds']"
        >删除</el-button
        >
      </el-col>
      <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
        border
        :height="heightCalc"
        :data="workflowList"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
          label="编号"
          align="center"
          prop="rationWorkflowConfigId"
      />
      <el-table-column
          label="所在单位"
          align="center"
          prop="organizationName"
      />
      <el-table-column
          label="模块名称"
          align="center"
          prop="nodeName"
      />
      <el-table-column label="一级审核角色" align="center" prop="auditAName" />
      <el-table-column label="二级审核角色" align="center" prop="auditBName" />
      <el-table-column label="三级审核角色" align="center" prop="auditCName" />
      <el-table-column label="四级审核角色" align="center" prop="auditDName" />
      <el-table-column label="五级审核角色" align="center" prop="auditEName" />
      <el-table-column label="六级审核角色" align="center" prop="auditFName" />
      <el-table-column label="七级审核角色" align="center" prop="auditGName" />
      <el-table-column label="八级审核角色" align="center" prop="auditHName" />
      <!--      <el-table-column-->
      <!--        label="审核级次"-->
      <!--        align="center"-->
      <!--        prop="auditLevelNo"-->
      <!--        :formatter="auditLevelNoFormat"-->
      <!--      />-->
      <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="120px"
      >
        <template #default= "scope">
          <el-button
              v-if="!(scope.row.gradecode.includes('gradecode02'))"
              type="text"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['rationworkflow:update']"
          >修改</el-button
          >
          <el-button
              v-if="!(scope.row.gradecode.includes('gradecode02'))"
              type="text"
              @click="handleDelete(scope.row)"
              v-hasPermi="['rationworkflow:deletebyid']"
          >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        :total="total"
        v-model:limit="queryParams.rows"
        v-model:page="queryParams.page"
        @pagination="getList"
    />

    <!-- 添加或修改基本田审核配置表对话框 -->
    <el-dialog :title="title" v-model="open" v-if="open" width="964px" append-to-body :close-on-click-modal="false">
      <div style="height: 400px;">
        <el-scrollbar style="height: 100%">
          <el-form ref="addForm" :model="form" :rules="rules" label-width="120px" label-position="top" style="margin-right: 10px;">
            <el-row style="display: flex; flex-wrap: wrap;" :gutter="10">
              <el-col :span="12">
                <el-form-item label="所在单位" prop="organizationNo">
                  <new-org-select
                      @handleOrgChange="handleOrgChangeAdd"
                      :currentOrgValue="form.organizationNo"
                      :showLevel=3
                  ></new-org-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模块名称" prop="lcRationWorkflowBillId">
                  <el-cascader
                      style="width: 100%;"
                      placeholder="请选择模块名称"
                      :key="refcraft"
                      :show-all-levels="true"
                      :props="{label:'name',value:'lcRationWorkflowBillId'}"
                      v-model="form.lcRationWorkflowBillId"
                      :options="billTypeoptions"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="一级审核角色" prop="auditARoleId">
                  <el-select
                      v-model="form.auditARoleId"
                      clearable
                      @clear="form.auditARoleId = null"
                      placeholder="请选择一级审核角色"
                      filterable

                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}1`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二级审核角色" prop="auditBRoleId">
                  <el-select
                      v-model="form.auditBRoleId"
                      clearable
                      @clear="form.auditBRoleId = null"
                      placeholder="请选择二级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}2`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="三级审核角色" prop="auditCRoleId">
                  <el-select
                      v-model="form.auditCRoleId"
                      clearable
                      @clear="form.auditCRoleId = null"
                      placeholder="请选择三级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}3`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="四级审核角色" prop="auditDRoleId">
                  <el-select
                      v-model="form.auditDRoleId"
                      clearable
                      @clear="form.auditDRoleId = null"
                      placeholder="请选择四级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}4`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="五级审核角色" prop="auditERoleId">
                  <el-select
                      v-model="form.auditERoleId"
                      clearable
                      @clear="form.auditERoleId = null"
                      placeholder="请选择五级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}5`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="六级审核角色" prop="auditFRoleId">
                  <el-select
                      v-model="form.auditFRoleId"
                      clearable
                      @clear="form.auditFRoleId = null"
                      placeholder="请选择六级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}6`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                    label="七级审核角色"
                    prop="auditGRoleId"
                    @clear="form.auditGRoleId = null"
                >
                  <el-select
                      v-model="form.auditGRoleId"
                      clearable
                      placeholder="请选择七级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}7`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="八级审核角色" prop="auditHRoleId">
                  <el-select
                      v-model="form.auditHRoleId"
                      clearable
                      @clear="form.auditHRoleId = null"
                      placeholder="请选择八级审核角色"
                      filterable
                  >
                    <el-option
                        v-for="(dict, index) in auditDeptNoFormat"
                        :key="`${dict.roleId}${index}8`"
                        :label="dict.roleName"
                        :value="dict.roleId.toString()"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="flag" :disabled="flag">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWorkflow,
  getWorkflow,
  delWorkflow,
  addWorkflow,
  updateWorkflow,
  delWorkflows,
  queryRoleList,
} from "@/api/systemlandcontract/config/workflow";
import {billqueryAll} from "@/api/systemlandcontract/config/bill";
import {checkPermi} from '@/utils/permission'
import minxins  from "@/views/systemlandcontract/utils/tableheight.js";
import NewOrgSelect from "@/views/systemlandcontract/components/NewOrgSelect/index.vue";
import {getDicts} from "@/api/systemlandcontract/system/dict/data";
export default {
  name: "/config/workflow/queryByPage",
  components: {NewOrgSelect},
  mixins: [minxins],
  data() {
    return {
      size:"medium",
      //选中模块名称合集
      nameList:[],
      loading: true,
      refcraft:0,
      //表格高度

      // 选中数组
      ids: [],
      yearNos: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 基本田审核配置表表格数据
      workflowList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 年度字典
      yearNoOptions: [],
      // 审核级次字典
      auditLevelNoOptions: [],
      // 一级审核角色字典
      auditADeptNoOptions: [],
      // 二级审核角色字典
      auditBDeptNoOptions: [],
      // 单据类型字典 模块名称
      billTypeNoOptions: [],
      auditDeptNoFormat: [],
      //加载图标状态
      flag: false,
      // 查询参数
      queryParams: {
        page: 1,
        rows: 10,
        // yearNo: null,
        organizationNo: null,
        // auditLevelNo: null,
        auditARoleId: null,
        gradecode: null,
        auditBRoleId: null,
        auditCRoleId: null,
        // auditAName: null,
        // auditBName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        auditARoleId: [
          { required: true, message: "请选择一级审核角色", trigger: "change" },
        ],
        lcRationWorkflowBillId: [
          { required: true, message: "请选择模块名称", trigger: "change" },
        ],
        organizationNo: [
          { required: true, message: "请选择所在单位", trigger: "change" },
        ],
      },
      billTypeoptions:[] //模块名称级联选择
    };
  },
  watch: {
    //搜索隐藏调整表格大小
    billTypeoptions(){
      ++this.refcraft
    }
  },
  created() {
    // this.getList();
    this.getbill()
    getDicts("bill_type").then((response) => {
      this.billTypeNoOptions = response.data;
    });
    queryRoleList().then((response) => {
      this.auditDeptNoFormat = response.data;
    });
  },
  methods: {
    /** 查询基本田审核配置表列表 */
    getList() {
      this.loading = true;
      let param = JSON.parse(JSON.stringify(this.queryParams))
      param.gradecode = param.gradecode?param.gradecode[param.gradecode.length-1]:null
      console.log(param)
      listWorkflow(param).then((response) => {
        this.workflowList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 模块名称
    getbill(){
      var _this = this;
      this.billTypeoptions = []
      billqueryAll().then(response => {
        if( response.data && response.data.length>0){
          response.data.forEach((v,i)=>{
            if(checkPermi([v.gradecode])){
              _this.billTypeoptions.push(v)
            }
          })
        }

      });
    },
    // 年度字典翻译
    yearNoFormat(row, column) {
      return this.selectDictLabel(
          this.yearNoOptions,
          row.yearNo != null && row.yearNo != undefined
              ? row.yearNo.toString()
              : ""
      );
    },
    // 审核级次字典翻译
    auditLevelNoFormat(row, column) {
      return this.selectDictLabel(
          this.auditLevelNoOptions,
          row.auditLevelNo != null && row.auditLevelNo != undefined
              ? row.auditLevelNo.toString()
              : ""
      );
    },
    // 一级审核角色字典翻译
    auditADeptNoFormat(row, column) {
      console.log(row, "===========row===========");
      for (let auditDeptNoFormatElement of this.auditDeptNoFormat) {
        if (row.auditARoleId == auditDeptNoFormatElement.roleId) {
          return auditDeptNoFormatElement.roleName;
        }
      }
    },
    // 二级审核角色字典翻译
    auditBDeptNoFormat(row, column) {
      console.log(row, "===========row===========");

      for (let auditDeptNoFormatElement of this.auditDeptNoFormat) {
        if (row.auditBRoleId == auditDeptNoFormatElement.roleId) {
          return auditDeptNoFormatElement.roleName;
        }
      }
    },
    // 三级审核角色字典翻译
    auditCDeptNoFormat(row, column) {
      console.log(row.auditCRoleId, "===========row===========");
      for (let auditDeptNoFormatElement of this.auditDeptNoFormat) {
        if (row.auditCRoleId == auditDeptNoFormatElement.roleId) {
          return auditDeptNoFormatElement.roleName;
        }
      }
    },

    // 取消按钮
    cancel() {
      this.reset();
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        rationWorkflowConfigId: null,
        // yearNo: null,
        filialeNo: null,
        farmNo: null,
        precinctNo: null,
        workstationNo: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        statusCd: null,
        creatorName: null,
        updaterName: null,
        organizationNo: null,
        organizationName: null,
        auditLevelNo: null,
        auditARoleId: null,
        lcRationWorkflowBillId: null,
        auditBRoleId: null,
        auditCRoleId: null,
        auditBName: null,
      };
      this.resetForm("addForm");
      this.getbill()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        page: 1,
        rows: 10,
        organizationNo: null,
        auditARoleId: null,
        auditBRoleId: null,
        auditCRoleId: null,
        gradecode:null
      };
      this.resetForm("queryForm");
      this.$refs.newOrgSelect.getCascader('reset')
      this.handleQuery();
      this.getbill()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.rationWorkflowConfigId);
      this.nameList = selection.map(item => item.nodeName);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.flag = false;
      this.reset();
      this.open = true;
      this.title = "新建审核流程设置";
    },
    //组织机构下拉
    handleOrgChange(orgCode, label) {
      this.queryParams.organizationNo = orgCode;
    },
    //组织机构下拉
    handleOrgChangeAdd(orgCode, label) {
      this.form.organizationNo = orgCode;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.flag = false;
      this.reset();
      const rationWorkflowConfigId = row.rationWorkflowConfigId || this.ids;
      getWorkflow(rationWorkflowConfigId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改审核流程设置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      let _this = this;
      this.$refs["addForm"].validate((valid) => {
        if (valid) {
          this.flag = false;
          for (let auditDeptNoFormatElement of this.auditDeptNoFormat) {
            if (_this.form.auditARoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditAName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditBRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditBName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditCRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditCName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditDRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditDName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditERoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditEName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditFRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditFName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditGRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditGName = auditDeptNoFormatElement.roleName;
            }
            if (_this.form.auditHRoleId == auditDeptNoFormatElement.roleId) {
              _this.form.auditHName = auditDeptNoFormatElement.roleName;
            }
          }
          if (!!this.form.auditCRoleId) {
            if (this.form.auditBRoleId == "" || this.form.auditBRoleId == null)
              return this.msgError("请选择二级审核角色");
          }
          if (!!this.form.auditDRoleId) {
            if (this.form.auditCRoleId == "" || this.form.auditCRoleId == null)
              return this.msgError("请选择三级审核角色");
          }
          if (!!this.form.auditERoleId) {
            if (this.form.auditDRoleId == "" || this.form.auditDRoleId == null)
              return this.msgError("请选择四级审核角色");
          }
          if (!!this.form.auditFRoleId) {
            if (this.form.auditERoleId == "" || this.form.auditERoleId == null)
              return this.msgError("请选择五级审核角色");
          }
          if (!!this.form.auditGRoleId) {
            if (this.form.auditFRoleId == "" || this.form.auditFRoleId == null)
              return this.msgError("请选择六级审核角色");
          }
          if (!!this.form.auditHRoleId) {
            if (this.form.auditGRoleId == "" || this.form.auditGRoleId == null)
              return this.msgError("请选择七级审核角色");
          }
          let param = JSON.parse(JSON.stringify(this.form))
          param.lcRationWorkflowBillId = param.lcRationWorkflowBillId && !param.lcRationWorkflowBillId == false?param.lcRationWorkflowBillId[param.lcRationWorkflowBillId.length-1]:null
          if (!!this.form.rationWorkflowConfigId) {
            this.flag = true;
            updateWorkflow(param).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkflow(param).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.flag = true;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const rationWorkflowConfigIds = row.rationWorkflowConfigId || this.ids;
      const nameList = row.nodeName || [...new Set(this.nameList)]
      this.$confirm(
          '是否确认删除模块名称为"' + nameList + '"的数据项?',
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
      )
          // this.$confirm(
          //   '是否确认删除编号为"' + rationWorkflowConfigIds + '"的数据项?',
          //   "警告",
          //   {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     type: "warning",
          //   }
          // )
          .then(function () {
            return Array.isArray(rationWorkflowConfigIds)
                ? delWorkflows(rationWorkflowConfigIds)
                : delWorkflow(rationWorkflowConfigIds);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          });
    },
    firstTripFun(){
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.el-cascader--medium{
  width: 100%;
}
:deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>
