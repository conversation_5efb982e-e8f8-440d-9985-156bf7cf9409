<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="68px" class="form-line">
            <el-row :gutter="30">
                <el-col :span="6">
                    <el-form-item label="作业年份" prop="year">
                        <el-select v-model="queryParams.year" placeholder="请选择年份">
                            <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode" label-width="110px">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" style="width: 100%;" @handleOrgCode="handleOrgChange"
                            :defaultOrgCode="queryParams.orgCode" :placeholderText="'请选择归属单位'"></OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="整机编号" prop="machineNo">
                        <el-input v-model="queryParams.machineNo" placeholder="请输入整机编号" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="牌照号" prop="licenseNo">
                        <el-input v-model="queryParams.licenseNo" clearable placeholder="请输入牌照号" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="农机分类" prop="amTypeAll">
                        <el-cascader style="width: 100%;" ref="orgSelect" v-model="amTypeAll" @change="amTypeAllChange" :options="machineList" placeholder="请选择农机分类"
                            :props="{
                                label: 'amTypeName',
                                value: 'amTypeCode',
                                expandTrigger: 'hover',
                                multiple: false,
                                checkStrictly: true,
                            }" :show-all-levels="false" collapse-tags clearable></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="配套农具型号" prop="machineToolModel" label-width="110px">
                        <el-input v-model="queryParams.machineToolModel" placeholder="请输入配套农具型号" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select style="width: 100%" v-model="queryParams.raiseCropsCd" clearable
                            placeholder="请选择种植作物">
                            <el-option v-for="dict in raiseCropsCdOptions" :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="生产流程" prop="prodProcessCode">
                        <el-select style="width: 100%" v-model="queryParams.prodProcessCode" clearable
                            placeholder="请选择生产流程">
                            <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select style="width: 100%" v-model="queryParams.linkCode" clearable placeholder="请选择作业环节">
                            <el-option v-for="dict in linkCodeOptions" :key="dict.linkCode" :label="dict.linkName"
                                :value="dict.linkCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="地块名称" prop="plotName" label-width="110px">
                        <el-input v-model="queryParams.plotName" clearable placeholder="请输入地块名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业时间">
                        <el-date-picker v-model="dateRange" style="width: 100%" value-format="YYYY-MM-DD"
                            type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="农机型号" prop="amModelName">
                        <el-input v-model="queryParams.amModelName" placeholder="请输入农机型号" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="机主姓名" prop="amOwnerName">
                        <el-input v-model="queryParams.amOwnerName" placeholder="请输入机主姓名" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="生产企业" prop="companyId" label-width="110px">
                        <loadSelect v-model="queryParams.companyId"></loadSelect>
                        <!-- <el-select v-model="queryParams.companyId" placeholder="请选择生产企业名称" clearable filterable
                            style="width: 100%">
                            <el-option v-for="(dict, index) in amcompanyList" :key="index" :label="dict.companyName"
                                :value="dict.companyId" />
                        </el-select> -->
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="农机产地" prop="machineOriginPlace">
                        <el-select v-model="queryParams.machineOriginPlace" placeholder="请选择农机产地" clearable>
                            <el-option v-for="dict in machine_origin_place" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="车辆类型" prop="machineOriginType">
                        <el-select v-model="queryParams.machineOriginType" placeholder="请选择车辆类型" clearable>
                            <el-option v-for="dict in machine_origin_type" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :lg="12" :xl="6" v-show="more">
                    <el-form-item label="马力区间" prop="minHorsePower">
                        <el-input-number v-model="queryParams.minHorsePower" placeholder="最小" clearable
                            style="width:calc(50% - 10px)" />
                        ~
                        <el-input-number v-model="queryParams.maxHorsePower" placeholder="最大" clearable
                            style="width:calc(50% - 10px)" />
                    </el-form-item>
                </el-col>
                <el-col :lg="more ? 12 : 6" :xl="more ? 18 : 6" class="text-right" align="right" style="margin-bottom: 12px;">
                    <el-button :icon="more ? 'ArrowUpBold' : 'ArrowDownBold'"
                        @click="moreHander">更多
                    </el-button>
                    <el-button icon="Refresh"  @click="resetQuery">重置
                    </el-button>
                    <el-button type="primary" icon="Search"  @click="handleQuery">搜索
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Plus"  v-hasPermi="['machineJobProgress:insert']"
                    @click="handleAdd">新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <FileUpload1 :isShowTip="false" v-hasPermi="['machineJobProgress:importExcel']" :default="['xls']"
                    :text1="'导入'" :uploadFileUrl="uploadFileUrl" @newlist="handleQuery"></FileUpload1>
            </el-col>
            <el-col :span="1.5">
                <el-button plain icon="Download" v-hasPermi="['machineJobProgress:importExcel']"
                    @click="exportTemplate">模板下载
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <exceldownload ref="exceldownload" v-hasPermi="['machineJobProgress:exportExcel']" :exceltext="'导出'"
                    icon="Download" :param="{ param }" :url="url"></exceldownload>
            </el-col>
            <el-col :span="1.5">
                <el-button plain icon="Upload" v-hasPermi="['machineJobProgress:infoConfig']"
                    @click="openConfig">上报配置</el-button>
            </el-col>
            <div style="display: flex;justify-content: flex-end;align-items: center;font-size: 14px;margin-left: auto;">
                <el-col :span="1.5" class="" style="margin-bottom: 0;">
                    累计作业面积：<span class="colorblue">{{ finalizePart }}</span>
                </el-col>
                <right-toolbar  v-model:showSearch="showSearch"
                    @queryTable="getList"></right-toolbar>
            </div>
        </el-row>
        <el-table ref="tableRef" :data="worksubtaskList"
            :height="tableHeight">
            <template v-for="item in columns">
                <el-table-column v-bind="item" :prop="item.key" :key="item.key" v-if="item.visible">
                    <template #default="scope">
                        <div v-if="item.key === 'amTypeName'">
                            {{
                                scope.row.amTypeName1 ? scope.row.amTypeName1 + '/' : ''
                            }}{{
                                scope.row.amTypeName2 ? scope.row.amTypeName2 + '/' : ''
                            }}{{ scope.row.amTypeName3 ? scope.row.amTypeName3 + '/' : '' }}{{ scope.row.amGradeName }}
                        </div>
                        <span v-else>{{ item.formatter ? item.formatter(scope.row, scope.column, scope.row[item.key],
                            scope.$index) : scope.row[item.key] }}</span>
                    </template>
                </el-table-column>
            </template>
            <el-table-column label="操作" align="center" fixed="right" width="180">
                <template #default="scope">
                    <el-button  link type="primary" icon="Search" @click="handleShow(scope.row)"
                        v-hasPermi="['machineJobProgress:info']">详情
                    </el-button>
                    <el-button  link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['machineJobProgress:update']">修改
                    </el-button>
                    <el-button  link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['machineJobProgress:logicDeleteById']">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
            @pagination="getList(true)" />
        <!-- 添加或修改作业记录（统管）对话框 -->
        <el-dialog :title="title" v-model="open" width="1080px" append-to-body @closed="cancel"
            :close-on-click-modal="false">
            <el-form ref="form" :model="form" label-width="120px" :disabled="title == '详情'" :rules="rules"
                class="form-style form-100" label-position="top">
                <el-row :gutter="20" style="display: flex;align-items: center;flex-wrap: wrap;">
                    <el-col :span="6">
                        <el-form-item label="归属" prop="orgCode">
                            <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" style="width: 100%;" :defaultOrgCode="form.orgCode" :placeholder="'请选择归属单位'"
                                @handleOrgCode="handleOrgChange2"></OrgSelects>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业年份" prop="year">
                            <el-select v-model="form.year" placeholder="请选择年份" style="width: 100%">
                                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                    :value="dict.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="车辆类型" prop="machineOriginType">
                            <el-select v-model="form.machineOriginType" placeholder="请选择车辆类型"
                                @change="machineOriginTypeChange" style="width: 100%">
                                <el-option v-for="dict in machine_origin_type" :key="dict.code" :label="dict.name"
                                    :value="dict.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" style="position: relative">
                        <el-form-item label="牌照号">
                            <el-input v-model="form.licenseNo" placeholder="" :disabled="formmachineOriginType" />
                        </el-form-item>
                        <el-button type="primary" v-if="title != '详情' && formmachineOriginType" class="select-btn"
                            @click="openSelecteForm(0)">
                            选择
                        </el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="农机分类" :prop="formmachineOriginType ? 'amTypeName' : 'amTypeAll'"
                            :title="`${form.amTypeName1 ? form.amTypeName1 + '/' : ''}${form.amTypeName2 ? form.amTypeName2 + '/' : ''}${form.amTypeName3 ? form.amTypeName3 + '/' : ''}${form.amGradeName ? form.amGradeName : ''}`">
                            <el-input v-if="formmachineOriginType"
                                :value="`${form.amTypeName1 ? form.amTypeName1 + '/' : ''}${form.amTypeName2 ? form.amTypeName2 + '/' : ''}${form.amTypeName3 ? form.amTypeName3 + '/' : ''}${form.amGradeName ? form.amGradeName : ''}`"
                                :placeholder="formmachineOriginType ? '请先选择牌照号，自动带出' : '请选择农机分类'"
                                :disabled="formmachineOriginType" />
                            <el-cascader v-else ref="orgSelect" v-model="form.amTypeAll" :options="machineList"
                                placeholder="请选择机具分类" :props="{
                                    label: 'amTypeName',
                                    value: 'amTypeCode',
                                    expandTrigger: 'hover',
                                    multiple: false,
                                    checkStrictly: true,
                                }" collapse-tags clearable></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="机主" :prop="formmachineOriginType ? 'amOwnerName' : ''">
                            <el-input v-model="form.amOwnerName"
                                :placeholder="formmachineOriginType ? '请先选择牌照号，自动带出' : '请输入机主'"
                                :disabled="formmachineOriginType" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="农机型号">
                            <el-input v-model="form.amModelName"
                                :placeholder="formmachineOriginType ? '请先选择牌照号，自动带出' : '请输入农机型号'"
                                :disabled="formmachineOriginType" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="生产企业">
                            <el-input v-if="formmachineOriginType" v-model="form.companyName" placeholder="请先选择牌照号，自动带出"
                                :disabled="formmachineOriginType" />
                            <!-- <el-select v-else v-model="form.companyId" placeholder="请选择生产企业名称" clearable filterable
                                style="width: 100%">
                                <el-option v-for="(dict, index) in amcompanyList" :key="index" :label="dict.companyName"
                                    :value="dict.companyId" />
                            </el-select> -->
                            <loadSelect v-else v-model="form.companyId"></loadSelect>
                        </el-form-item>
                    </el-col>
                </el-row>
                <template v-for="(item, index) in form.machs">
                    <el-row style="margin-top: 20px" :gutter="20">
                        <el-col :span="24">
                            <div class="dialog-tab">
                                <div style="display: flex;align-items: center;">作业地块<span v-if="title != '详情'"> -{{
                                    index + 1 }}</span></div>
                                <div>
                                    <el-button type="primary"  @click="addMachine"
                                        v-if="index == 0 && title == '新增'">新增
                                    </el-button>
                                    <el-button type="primary"  @click="popMachine(index)"
                                        v-if="form.machs && form.machs.length > 1">删除
                                    </el-button>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6" style="position: relative">
                            <el-form-item label="地块名称" :prop="'machs.' + index + '.plotName'" :rules="rules.plotName">
                                <el-input v-model="item.plotName" placeholder="" disabled />
                            </el-form-item>
                            <el-button type="primary" v-if="title != '详情'" class="select-btn" style="bottom: 12px;"
                                @click="openSelecteFormPlot(index)"> 选择
                            </el-button>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="种植作物" :prop="'machs.' + index + '.raiseCropsCd'"
                                :rules="rules.raiseCropsCd">
                                <el-input v-model="item.raiseCropsNm" placeholder="请先选择地块，自动带出" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="生产流程" :prop="'machs.' + index + '.prodProcessCode'"
                                :rules="rules.prodProcessCode">
                                <el-input v-model="item.prodProcessName" placeholder="请先选择地块，自动带出" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业环节" :prop="'machs.' + index + '.linkCode'" :rules="rules.linkCode">
                                <el-input v-model="item.linkName" placeholder="请先选择地块，自动带出" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="地块面积(亩)" :rules="rules.plotArea"
                                :prop="'machs.' + index + '.plotArea'">
                                <el-input v-model="item.plotArea" disabled placeholder="请先选择地块，自动带出" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="配套农具型号" :rules="rules.machineToolModel"
                                :prop="'machs.' + index + '.machineToolModel'">
                                <el-input v-model="item.machineToolModel" placeholder="请输入配套农具型号" maxlength="20" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业面积(亩)" :rules="rules.workArea"
                                :prop="'machs.' + index + '.workArea'">
                                <el-input v-model="item.workArea" placeholder="请输入作业面积" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业开始时间" :prop="'machs.' + index + '.jobStartTime'"
                                :rules="rules.jobStartTime">
                                <el-date-picker style="width: 100%;" v-model="item.jobStartTime"
                                   value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择作业开始时间" type="datetime"
                                    clearable></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业结束时间" :prop="'machs.' + index + '.jobEndTime'"
                                :rules="rules.jobEndTime">
                                <el-date-picker style="width: 100%;" v-model="item.jobEndTime"
                                   value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择作业结束时间" type="datetime"
                                    clearable></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </template>
            </el-form>
            <template #footer class="dialog-footer">
                <div class="dialog-footer">
                    <el-button type="primary" v-show="title != '详情'" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--   地块     -->
        <el-dialog v-model="showPlot" title="选择地块" width="1080px" @close="closePlot">
            <el-form ref="plotForm" :model="plotParams" label-width="120px" v-if="showPlot">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="种植作物" prop="raiseCropsCd" :rules="rules.raiseCropsCd">
                            <el-select v-model="plotParams.raiseCropsCd" clearable placeholder="请选择种植作物">
                                <el-option v-for="dict in raiseCropsCdOptionsPlot" :key="dict.raiseCropsCd"
                                    :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="生产流程" prop="prodProcessCode" :rules="rules.prodProcessCode">
                            <el-select v-model="plotParams.prodProcessCode" clearable placeholder="请选择生产流程">
                                <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name"
                                    :value="dict.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业环节" prop="linkCode" :rules="rules.linkCode">
                            <el-select v-model="plotParams.linkCode" clearable placeholder="请选择作业环节">
                                <el-option v-for="dict in linkCodeOptionsPlot" :key="dict.linkCode"
                                    :label="dict.linkName" :value="dict.linkCode" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="地块名称" prop="plotName">
                            <el-input v-model="plotParams.plotName" @clear="handleCheckedChange"
                                @input="handleCheckedChange" placeholder="请填写地块名称">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="max-height: 600px;overflow-y: auto">
                <el-radio-group v-model="checkboxGroup" class="plot-dia">
                    <el-radio v-for="(item, index) in plotListFilter" :key="index" :label="item.plotNo" border
                        style="width: 276px;margin:5px;display: flex;align-items: center;">{{ item.plotName }}{{ item.pastPlotName == null ? '' : '(' +
                            item.pastPlotName + ')' }}
                    </el-radio>
                </el-radio-group>
                <div v-if="!plotListFilter || plotListFilter.length == 0" style="text-align: center">暂无相关地块</div>
            </div>
            <template #footer class="dialog-footer">
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleApply">确 定</el-button>
                    <el-button @click="closePlot">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--   上报配置     -->
        <el-dialog v-model="showConfig" title="上报配置" width="764px" @close="closeConfig">
            <el-form ref="configForm" :rules="configRules" :model="configForm" label-width="120px" label-position="top">
                <el-row :gutter="12">
                    <el-col :span="8">
                        <el-form-item label="上报日期限制" prop="useStatus">
                            <el-radio-group v-model="configForm.useStatus">
                                <el-radio :label="true">开</el-radio>
                                <el-radio :label="false">关</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="configForm.useStatus">
                        <el-form-item label="限制维度" prop="jobLimitDay">
                            <el-select v-model="configForm.jobLimitDay" clearable placeholder="请选择限制维度">
                                <el-option label="当日" :value="0" />
                                <el-option label="次日" :value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="configForm.useStatus">
                        <el-form-item label="限制时间" prop="jobLimitHour">
                            <el-select v-model="configForm.jobLimitHour" clearable placeholder="请选择限制时间">
                                <el-option v-for="item in configFormTime" :key="item" :label="item + '时'"
                                    :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer class="dialog-footer">
                <div class="dialog-footer">
                    <el-button type="primary" @click="saveConfig">确 定</el-button>
                    <el-button @click="closeConfig">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--   修改-绑定农机     -->
        <bindDialog @changeForm="selectForm" :amMachineFlag="1" v-if="showMachine" :is-edit="true" v-model:value="showMachine"
        />
    </div>
</template>
<script>
import { queryResPlotInfo } from '@/api/systemagriculturalmachineryv2/workApply'
import { queryDictTree } from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'
import { cropProcess } from '@/api/systemagriculturalmachineryv2/dispatch/operational'
import { listAmcompanyAll } from "@/api/systemagriculturalmachineryv2/basicInfo/amcompany";
import FileUpload1 from '@/views/systemagriculturalmachineryv2/components/FileUpload1'
import exceldownload from '@/views/systemagriculturalmachineryv2/components/exceldownload'
import bindDialog from '../../basicInfo/registerRelation/bindDialog'
import { format } from 'date-fns'
import useUserStore from "@/store/modules/user"
import { getDicts, getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import { selectDictLabelForRaiseCrops,selectDictLabelForLink } from "@/utils/cop";
import { selectDictLabel } from '@/utils/cop'
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
import { listMachineJobProgress, getMachineJobProgress, delMachineJobProgress, addMachineJobProgress, updateMachineJobProgress, delMachineJobProgresss, exportTemplate, areaStat, infoConfig, updateConfig } from "@/api/systemagriculturalmachineryv2/schedulingPlan/machineJobProgress";
import {orgDictsUrl} from "@/api/systemagriculturalmachineryv2/orgDict.js";
import loadSelect from "@/views/systemagriculturalmachineryv2/components/loadSelect/loadSelect";
export default {
    name: '/schedulingPlan/machineJobProgress/queryByPage',
    components: {
        FileUpload1, exceldownload, bindDialog, loadSelect
    },
    mixins: [calcTabelHeight],
    computed: {
        param() {
            let obj = {
                ...this.queryParams
            }
            obj.amTypeCode1 = this.amTypeAll[0] || "";
            obj.amTypeCode2 = this.amTypeAll[1] || "";
            obj.amTypeCode3 = this.amTypeAll[2] || "";
            obj.amGradeId = this.amTypeAll[3] || "";
            return obj
        },
        formmachineOriginType() {
            //返回编辑弹窗中的车辆类型是否是“本单位车辆”，这个影响一些必填项规则和提示信息
            let value = this.form.machineOriginType;
            return this.form.machineOriginType == "1" ||
                (value === null || value === '' || typeof (value) == 'undefined' || value.toString().trim().length == 0);
        }
    },
    data() {
        var amOwnerNameValidate = (rule, value, callback) => {
            if (value === null || value === '' || typeof (value) == 'undefined' || value.trim().length == 0) {
                callback(new Error('机主不能为空'))
            } else {
                callback()
            }
        }
        var passAreaValidateNum = (rule, value, callback) => {
            var reg = /^[0-9]+(\.?[0-9]{1,4})?$/
            if (value === null || value === '' || typeof (value) == 'undefined') {
                callback(new Error('地块面积不能为空'))
            } else if (value.toString().split('.')[0].length > 6) {
                callback(new Error('数字太大，请确认'))
            } else if (reg.test(value)) {
                callback()
            } else {
                callback(new Error('请输入正数(最多带四位小数)'))
            }
        }
        var workAreaValidateNum = (rule, value, callback, source) => {
            let index = rule.field.split(".")[1]
            let data = this.form.machs[index]
            var reg = /^[0-9]+(\.?[0-9]{1,4})?$/
            if (value === null || value === '' || typeof (value) == 'undefined') {
                callback(new Error('请输入作业面积（亩）'))
            } else if (value.toString().split('.')[0].length > 6) {
                callback(new Error('数字太大，请确认'))
            } else if (reg.test(value)) {
                if (data.workArea > data.plotArea) {
                    callback(new Error('作业面积不可大于地块面积'))
                } else {
                    callback()
                }
            } else {
                callback(new Error('请输入正数(最多带四位小数)'))
            }
        }
        var amTypeNameValidate = (rule, value, callback) => {
            if (value === null || value === '' || typeof (value) == 'undefined' || value.trim().length == 0) {
                callback(new Error('农机分类不能为空'))
            } else if (
                (this.form.amTypeName1 === null || this.form.amTypeName1 === '' || typeof (this.form.amTypeName1) == 'undefined' || this.form.amTypeName1.trim().length == 0)
                ||
                (this.form.amTypeName2 === null || this.form.amTypeName2 === '' || typeof (this.form.amTypeName2) == 'undefined' || this.form.amTypeName2.trim().length == 0)
            ) {
                callback(new Error('农机大类、农机小类不能为空'))
            } else {
                callback()
            }
        }
        var amTypeAllValidate = (rule, value, callback) => {
            if (value === null || value === '' || typeof (value) == 'undefined' || value.length == 0) {
                callback(new Error('农机分类不能为空'))
            } else if (
                (value[0] === null || value[0] === '' || typeof (value[0]) == 'undefined' || value[0].trim().length == 0)
                ||
                (value[1] === null || value[1] === '' || typeof (value[1]) == 'undefined' || value[1].trim().length == 0)
                ||
                (value[2] === null || value[2] === '' || typeof (value[2]) == 'undefined' || value[2].trim().length == 0)
                ||
                (value[3] === null || value[3] === '' || typeof (value[3]) == 'undefined' || value[3].trim().length == 0)
            ) {
                callback(new Error('农机大类、农机小类、机具品目、机具分档不能为空'))
            } else {
                callback()
            }
        }
        return {
            amcompanyList: [],
            dateRange: [],
            // 农机产地
            machine_origin_place: [],
            more: false,
            showConfig: false,
            columns: [
                {
                    label: '归属',
                    align: 'center',
                    key: 'orgName',
                    width: '220',
                    visible: true,
                    fixed: 'left',
                },
                {
                    label: '作业年份',
                    align: 'center',
                    key: 'year',
                    width: '120',
                    visible: true,
                    fixed: 'left',
                },
                {
                    label: '整机编号',
                    align: 'center',
                    key: 'machineNo',
                    width: '120',
                    visible: true,
                    fixed: 'left',
                },
                {
                    label: '牌照号',
                    align: 'center',
                    key: 'licenseNo',
                    width: '120',
                    visible: true,
                },
                {
                    label: '机主',
                    align: 'center',
                    key: 'amOwnerName',
                    width: '120',
                    visible: true,
                },
                {
                    label: '农机分类',
                    align: 'center',
                    key: 'amTypeName',
                    width: '120',
                    visible: true,
                },
                {
                    label: '车辆类型',
                    align: 'center',
                    key: 'machineOriginType',
                    formatter: this.formatter,
                    width: '135',
                    visible: true,
                },
                {
                    label: '农机型号',
                    align: 'center',
                    key: 'amModelName',
                    width: '180',
                    visible: true,
                },
                {
                    label: '生产企业',
                    align: 'center',
                    key: 'companyName',
                    width: '180',
                    visible: true,
                },
                {
                    label: '马力/功率(千瓦)',
                    align: 'center',
                    key: 'selfAndHorsePower',
                    width: '180',
                    visible: true,
                },
                {
                    label: '农机产地',
                    align: 'center',
                    key: 'machineOriginPlace',
                    formatter: this.formatter,
                    width: '100',
                    visible: true,
                },
                {
                    label: '配套农具型号',
                    align: 'center',
                    key: 'machineToolModel',
                    width: '100',
                    visible: true,
                },
                {
                    label: '地块名称',
                    align: 'center',
                    key: 'plotName',
                    width: '100',
                    visible: true,
                },
                {
                    label: '地块面积(亩)',
                    align: 'center',
                    key: 'plotArea',
                    width: '100',
                    visible: true,
                },
                {
                    label: '种植作物',
                    align: 'center',
                    key: 'raiseCropsNm',
                    width: '100',
                    visible: true,
                },
                {
                    label: '生产流程',
                    align: 'center',
                    key: 'prodProcessName',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业环节',
                    align: 'center',
                    key: 'linkName',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业面积(亩)',
                    align: 'center',
                    key: 'workArea',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业开始时间',
                    align: 'center',
                    key: 'jobStartTime',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业结束时间',
                    align: 'center',
                    key: 'jobEndTime',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业时长（小时）',
                    align: 'center',
                    key: 'jobTotalTime',
                    width: '140',
                    visible: true,
                },
                {
                    label: '操作人',
                    align: 'center',
                    key: 'operatorName',
                    width: '100',
                    visible: true,
                },
                {
                    label: '上报时间',
                    align: 'center',
                    key: 'createTime',
                    formatter: this.formatter,
                    width: '100',
                    visible: true,
                },
            ],
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 作业补录（统管）表格数据
            worksubtaskList: [],
            spanArr: [],
            pos: null,
            openInfo: false,
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                rows: 10,
                year: String(new Date().getFullYear()),
                orgCode: useUserStore().currentOrgCode,
                machineNo: null,
                licenseNo: null,
                amModelName: null,
                machineToolModel: null,
                amOwnerName: null,
                companyId: null,
                machineOriginPlace: null,
                machineOriginType: null,
                plotNo: null,
                plotName: null,
                plotArea: null,
                landType: null,
                raiseCropsCd: null,
                raiseCropsNm: null,
                prodProcessCode: null,
                prodProcessName: null,
                linkCode: null,
                linkName: null,
                workArea: null,
                jobStartTime: null,
                jobEndTime: null,
                jobTotalTime: null,
                operatorId: null,
                operatorName: null,
                minHorsePower: null,
                maxHorsePower: null,
            },
            // 表单参数
            form: {
                amTypeName: null,
                amTypeCode1: null,
                amTypeCode2: null,
                amTypeCode3: null,
                amGradeId: null,
                amTypeAll: [],
                companyName: null,
                licenseNo: null,
                companyId: null,
                amModelName: null,
                amOwnerName: null,
                machineJobProgressId: null,
                year: null,
                machineOriginType: "1",
                machineId: null,
                orgCode: null,
                orgName: null,
                plotNo: null,
                plotName: null,
                plotArea: null,
                landType: null,
                raiseCropsCd: null,
                raiseCropsNm: null,
                prodProcessCode: null,
                prodProcessName: null,
                linkCode: null,
                linkName: null,
                workArea: null,
                jobStartTime: null,
                jobEndTime: null,
                machs: [{
                    plotNo: null,
                    plotName: null,
                    plotArea: null,
                    landType: null,
                    raiseCropsCd: null,
                    raiseCropsNm: null,
                    prodProcessCode: null,
                    prodProcessName: null,
                    linkCode: null,
                    linkName: null,
                    workArea: null,
                    jobStartTime: null,
                    jobEndTime: null,
                    machineToolModel: null,
                }]
            },
            machsIndex: null,
            // 表单校验
            rules: {
                statYear: [
                    {
                        required: true,
                        message: '请选择作业年份',
                        trigger: 'change'
                    }
                ],
                year: [
                    {
                        required: true,
                        message: '请选择所属年份',
                        trigger: 'change'
                    }
                ],
                machineOriginType: [
                    {
                        required: true,
                        message: '请选择车辆类型',
                        trigger: 'change'
                    }
                ],
                orgCode: [
                    {
                        required: true,
                        message: '请选择归属单位',
                        trigger: 'change'
                    }
                ],
                plotName: [
                    {
                        required: true,
                        message: '请选择地块',
                        trigger: 'change'
                    }
                ],
                raiseCropsCd: [
                    {
                        required: true,
                        message: '种植作物不能为空',
                        trigger: 'change'
                    }
                ],
                prodProcessCode: [
                    {
                        required: true,
                        message: '生产流程不能为空',
                        trigger: 'change'
                    }
                ],
                linkCode: [
                    {
                        required: true,
                        message: '作业环节不能为空',
                        trigger: 'change'
                    }
                ],
                machineToolModel: [
                    {
                        required: true,
                        message: '请输入配套农具型号',
                        trigger: 'change'
                    }
                ],
                plotName: [
                    {
                        required: true,
                        message: '地块名称不能为空',
                        trigger: 'change'
                    }
                ],
                amOwnerName: [
                    {
                        required: true,
                        validator: amOwnerNameValidate,
                        trigger: 'change'
                    }
                ],
                licenseNo: [{ required: true, trigger: 'change', message: '请选择农机' }],
                jobStartTime: [{ required: true, trigger: 'blur', message: '请选择作业开始时间' }],
                jobEndTime: [{ required: true, trigger: 'blur', message: '请选择作业结束时间' }],
                plotArea: [{ required: true, validator: passAreaValidateNum, trigger: 'change' }],
                workArea: [{
                    required: true,
                    validator: workAreaValidateNum,
                    trigger: 'change'
                }],
                amTypeName: [{
                    required: true,
                    validator: amTypeNameValidate,
                    trigger: 'change'
                }],
                amTypeAll: [{
                    required: true,
                    validator: amTypeAllValidate,
                    trigger: 'change'
                }],
            },
            url: window.VITE_APP_BASE_API + '/'+import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE+'/schedulingPlan/machineJobProgress/exportExcel',
            //导入地址
            uploadFileUrl: window.VITE_APP_BASE_API + '/'+import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE+'/schedulingPlan/machineJobProgress/importExcel',
            //种植作物
            raiseCropsCdOptions: [],
            //种植作物
            raiseCropsCdOptionsPlot: [],
            // 作业环节
            linkCodeOptions: [],
            // 作业环节- 地块选择
            linkCodeOptionsPlot: [],
            //成产流程
            prodProcessOptions: [],
            // 年度字典
            yearNoOptions: [],
            amTypeAll: [], //总分类
            //农机分类
            machineList: [],
            //车辆类型
            machine_origin_type: [],
            // { name: "本单位车辆", code: 1 },
            // { name: "垦区内-外来车辆", code: 2 },
            // { name: "垦区外-外来车辆", code: 3 },
            //农机选择
            showMachine: false,
            //地块选择
            plotParams: {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            },
            checkboxGroup: '',
            showPlot: false,
            plotListFilter: [],
            finalizePart: '0',
            //上报配置的表单
            configForm: {
                useStatus: false,
                jobLimitDay: null, //当日 0 次日 1
                jobLimitHour: null
            },
            configRules: {
                useStatus: [
                    {
                        required: true,
                        message: '请选择上报日期限制',
                        trigger: 'change'
                    }
                ],
                jobLimitDay: [
                    {
                        required: true,
                        message: '请选择限制维度',
                        trigger: 'change'
                    }
                ],
                jobLimitHour: [
                    {
                        required: true,
                        message: '请选择限制时间',
                        trigger: 'change'
                    }
                ],
            },
            configFormTime: [], //上报配置 - 限制时间 选项值
            nowTime: new Date().getTime()
        }
    },
    created() {
        listAmcompanyAll({}).then(response => {
            this.amcompanyList = response.data;
        });
        getDicts('year_cd').then(response => {
            this.yearNoOptions = response.data
        })
        getDicts('prod_process').then(response => {
            this.prodProcessOptions = response.data
        })
        queryDictTree('1').then((res) => {
            this.machineList = res.data
        })
        getDicts("machine_origin_place").then(response => {
            this.machine_origin_place = response.data;
        });
        getDicts("machine_origin_type").then(response => {
            this.machine_origin_type = response.data;
        });
        // infoConfig().then(response => {
        //     this.configForm = response.data;
        // });
        //获取车辆类型，暂时没有接口和参数名定义
        // getDicts("machineOriginType").then(response => {
        //     this.machine_origin_type = response.data;
        // });
        for (let i = 1; i < 24; i++) {
            this.configFormTime.push(i)
        }
        this.getList()
        this.getRaise()
    },
    watch: {
        /**监控年份变化**/
        'queryParams.year'(val, oldVal) {
            if (val != null && val !== '' && !!this.queryParams.year) {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.orgCode'(val, oldVal) {
            if (val != null && val !== '') {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.raiseCropsCd'(val, oldVal) {
            this.queryParams.linkCode = null
            if (val != null && val !== '' && this.queryParams.prodProcessCode != null && this.queryParams.prodProcessCode !== '') {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.queryParams.prodProcessCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            } else {
                this.linkCodeOptions = []
            }
        },
        'queryParams.prodProcessCode'(val, oldVal) {
            this.queryParams.linkCode = null
            if (val != null && val !== '' && this.queryParams.raiseCropsCd != null && this.queryParams.raiseCropsCd !== '') {
                //查机具品目
                cropProcess({
                    raiseCrops: this.queryParams.raiseCropsCd,
                    prodProcessCode: val,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            } else {
                this.linkCodeOptions = []
            }
        },
        'plotParams.linkCode'(val, old) {
            this.selectPlot()
        },
        'plotParams.raiseCropsCd'(val, oldVal) {
            this.plotParams.linkCode = null
            if (val != null && val !== '' && this.plotParams.prodProcessCode != null && this.plotParams.prodProcessCode !== '') {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.plotParams.prodProcessCode,
                    orgCode: this.form.orgCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptionsPlot = response.data
                })
            } else {
                this.linkCodeOptionsPlot = []

            }
        },
        'plotParams.prodProcessCode'(val, oldVal) {
            this.plotParams.linkCode = null
            if (val != null && val !== '' && this.plotParams.raiseCropsCd != null && this.plotParams.raiseCropsCd !== '') {
                //查机具品目
                cropProcess({
                    raiseCrops: this.plotParams.raiseCropsCd,
                    prodProcessCode: val,
                    orgCode: this.form.orgCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptionsPlot = response.data
                })
            } else {
                this.linkCodeOptionsPlot = []
            }
        },
        'form.amTypeAll'(val, oldVal) {
            if (this.open && this.title != '详情') {
                // console.log(val);
                this.form.amTypeCode1 = val[0]
                this.form.amTypeCode2 = val[1]
                this.form.amTypeCode3 = val[2]
                this.form.amGradeId = val[3]
            }
        }
    },
    methods: {
      orgDictsUrl,
        amTypeAllChange(value){
            if(!value){
                this.amTypeAll = []
            }
        },
        /** 格式化表格 */
        formatter(row, column, cellValue, index) {
            if (column.property == 'amStatus') {
                return selectDictLabel(this.amStatusOptions, cellValue);
            } else if (column.property == 'formationType') {
                return selectDictLabel(this.pubIfOptions, cellValue);
            } else if (column.property == 'powerMachine') {
                return selectDictLabel(this.powerMachineOptions, cellValue);
            } else if (column.property == 'machineOriginPlace') {
                return selectDictLabel(this.machine_origin_place, cellValue);
            } else if (column.property == 'machineOriginType') {
                return selectDictLabel(this.machine_origin_type, cellValue);
            } else if (column.property == 'selfPower') {
                if (cellValue) {
                    var horsepower = cellValue * 1.36;
                    var kilowatt = cellValue;
                    return horsepower.toFixed(0) + '/' + kilowatt;
                }
                return cellValue
            } else if (column.property == 'createTime') {
                return format(new Date(cellValue), 'yyyy-MM-dd HH:mm:ss');
            } else {
                return row[column.property]
            }
        },
        moreHander() {
            this.more = !this.more
        },
        getRaise() {
            //作物
            this.raiseCropsCdOptions = []
            this.queryParams.raiseCropsCd = ''
            this.queryParams.raiseCropsNm = ''
            getRaiseCrops({
                orgCode: this.queryParams.orgCode,
                year: this.queryParams.year
            }).then(response => {
                this.raiseCropsCdOptions = response.data
            })

        },
        //搜索地块
        handleCheckedChange(e) {
            if (!this.plotParams.plotName) {
                this.plotListFilter = this.plotList
            } else {
                this.plotListFilter = this.plotList.filter(item => {
                    return item.plotName.includes(this.plotParams.plotName)
                })
            }
        },
        //地块列表-选择地块
        selectPlot() {
            this.$refs['plotForm'].validate(valid => {
                if (valid) {
                    queryResPlotInfo(this.plotParams).then(res => {
                        this.plotList = res.data
                        this.plotListFilter = res.data
                        this.plotName = ''
                        this.checkboxGroup = ''
                    })
                }
            })
        },
        resetQueryPlot() {
            this.plotParams = {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            }
            this.selectPlot()
        },
        //选择地块
        openSelecteFormPlot(index) {
            if (!this.form.orgCode) {
                this.$message({
                    message: '请选择归属单位',
                    type: 'warning'
                })
                return
            }
            if (!this.form.year) {
                this.$message({
                    message: '请选择作业年份',
                    type: 'warning'
                })
                return
            }
            this.machsIndex = index
            this.showPlot = true
            this.plotParams.orgCode = this.form.orgCode
            this.plotParams.statYear = this.form.year
            //作物
            this.raiseCropsCdOptionsPlot = []
            this.plotParams.raiseCropsCd = ''
            getRaiseCrops({
                orgCode: this.plotParams.orgCode,
                year: this.plotParams.statYear
            }).then(response => {
                this.raiseCropsCdOptionsPlot = response.data
            })
        },
        closePlot() {
            this.plotParams = {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            }
            this.showPlot = false
            this.checkboxGroup = ''
            this.plotListFilter = []
            this.resetForm('plotForm')
        },
        //确定选择地块
        handleApply() {
            if (!this.checkboxGroup) {
                this.$modal.msgError('请选择地块')
                return
            }
            let name = this.plotListFilter.filter(item => {
                return item.plotNo == this.checkboxGroup
            })
            this.form.machs[this.machsIndex].plotName = `${name[0].plotName}${name[0].pastPlotName ? `(${name[0].pastPlotName})` : ''}`
            this.form.machs[this.machsIndex].plotNo = name[0].plotNo
            this.form.machs[this.machsIndex].landType = name[0].landType
            this.form.machs[this.machsIndex].plotArea = name[0].contrArea
            this.form.machs[this.machsIndex].raiseCropsCd = this.plotParams.raiseCropsCd
            this.form.machs[this.machsIndex].prodProcessCode = this.plotParams.prodProcessCode
            this.form.machs[this.machsIndex].linkCode = this.plotParams.linkCode
            this.form.machs[this.machsIndex].linkName = selectDictLabelForLink(this.linkCodeOptionsPlot, this.form.machs[this.machsIndex].linkCode)
            this.form.machs[this.machsIndex].prodProcessName = selectDictLabel(this.prodProcessOptions, this.form.machs[this.machsIndex].prodProcessCode)
            this.form.machs[this.machsIndex].raiseCropsNm = selectDictLabelForRaiseCrops(this.raiseCropsCdOptionsPlot, this.form.machs[this.machsIndex].raiseCropsCd)
            this.closePlot()
        },
        //地块弹窗
        openSelecteForm() {
            this.showMachine = true
        },
        //添加地块
        addMachine() {
            this.form.machs.push(
                {
                    plotNo: null,
                    plotName: null,
                    plotArea: null,
                    landType: null,
                    raiseCropsCd: null,
                    raiseCropsCd: null,
                    raiseCropsNm: null,
                    prodProcessCode: null,
                    prodProcessName: null,
                    linkCode: null,
                    linkCode: null,
                    linkName: null,
                    workArea: null,
                    jobStartTime: null,
                    jobEndTime: null,
                }
            )
        },
        popMachine(index) {
            this.form.machs.splice(index, 1)
        },
        //选择农机-回调
        selectForm(e) {
            this.form.licenseNo = e.licenseNo
            this.form.machineId = e.machineId
            this.form.companyName = e.companyName
            this.form.amOwnerName = e.amOwnerName
            this.form.amTypeName1 = e.amTypeName1
            this.form.amTypeName2 = e.amTypeName2
            this.form.amTypeName3 = e.amTypeName3
            this.form.amGradeName = e.amGradeName
            this.form.amTypeCode1 = e.amTypeCode1
            this.form.amTypeCode2 = e.amTypeCode2
            this.form.amTypeCode3 = e.amTypeCode3
            this.form.amGradeId = e.amGradeId
            // this.form.amTypeAll = [
            //     e.amTypeCode1,
            //     e.amTypeCode2,
            //     e.amTypeCode3,
            //     e.amGradeId
            // ]
            this.form.amModelName = e.amModelName
            this.form.amTypeName = `${this.form.amTypeName1}${this.form.amTypeName2}${this.form.amTypeName3}${this.form.amGradeName}`
            this.showMachine = false
        },
        getareaStat() {
            this.queryParams.jobStartTime = (this.dateRange && this.dateRange[0]) ? this.dateRange[0] + ' 00:00:00' : ''
            this.queryParams.jobEndTime = (this.dateRange && this.dateRange[1]) ? this.dateRange[1] + ' 23:59:59' : ''
            areaStat(this.queryParams).then(response => {
                this.finalizePart = response.data
            })
        },
        /** 查询作业记录（非统管）列表 */
        getList(bool) {
            this.loading = true
            this.queryParams.amTypeCode1 = this.amTypeAll[0] || "";
            this.queryParams.amTypeCode2 = this.amTypeAll[1] || "";
            this.queryParams.amTypeCode3 = this.amTypeAll[2] || "";
            this.queryParams.amGradeId = this.amTypeAll[3] || "";
            this.queryParams.jobStartTime = (this.dateRange && this.dateRange[0]) ? this.dateRange[0] + ' 00:00:00' : ''
            this.queryParams.jobEndTime = (this.dateRange && this.dateRange[1]) ? this.dateRange[1] + ' 23:59:59' : ''
            listMachineJobProgress(this.queryParams).then(response => {
                this.worksubtaskList = response.data.records
                this.total = response.data.total
                this.loading = false
            })
            if (!bool) {
                this.getareaStat()
            }
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            //表格合并行
            if (
                columnIndex === 0 ||
                columnIndex === 1 ||
                columnIndex === 2 ||
                columnIndex === 3 ||
                columnIndex === 4 ||
                columnIndex === 5 ||
                columnIndex === 17
            ) {
                const _row = this.spanArr[rowIndex]
                const _col = _row > 0 ? 1 : 0
                return {
                    rowspan: _row,
                    colspan: _col
                }
            }
        },
        getSpanArr() {
            this.spanArr = []
            let data = this.worksubtaskList
            for (let i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1)
                    this.pos = 0
                } else {
                    // 判断当前元素与上一个元素是否相同
                    if (data[i].machineJobProgressId === data[i - 1].machineJobProgressId) {
                        this.spanArr[this.pos] += 1
                        this.spanArr.push(0)
                    } else {
                        this.spanArr.push(1)
                        this.pos = i
                    }
                }
            }
        },
        // 取消按钮
        cancel() {
            this.reset()
            this.open = false
        },
        // 表单重置
        reset() {
            this.form = {
                amTypeName: null,
                amTypeCode1: null,
                amTypeCode2: null,
                amTypeCode3: null,
                amGradeId: null,
                amTypeAll: [],
                licenseNo: null,
                companyName: null,
                companyId: null,
                amModelName: null,
                amOwnerName: null,
                machineJobProgressId: null,
                year: null,
                machineOriginType: "1",
                machineId: null,
                orgCode: null,
                orgName: null,
                plotNo: null,
                plotName: null,
                plotArea: null,
                landType: null,
                raiseCropsCd: null,
                raiseCropsNm: null,
                prodProcessCode: null,
                prodProcessName: null,
                linkCode: null,
                linkName: null,
                workArea: null,
                jobStartTime: null,
                jobEndTime: null,
                machs: [{
                    plotNo: null,
                    plotName: null,
                    plotArea: null,
                    landType: null,
                    raiseCropsCd: null,
                    raiseCropsNm: null,
                    prodProcessCode: null,
                    prodProcessName: null,
                    linkCode: null,
                    linkName: null,
                    workArea: null,
                    jobStartTime: null,
                    jobEndTime: null,
                    machineToolModel: null,
                }]
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1
            this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm')
            this.amTypeAll = []
            this.dateRange = []
            this.queryParams.minHorsePower = null
            this.queryParams.maxHorsePower = null
            this.handleQuery()
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.form.year = String(new Date().getFullYear())
            this.form.orgCode = useUserStore().currentOrgCode
            this.title = '新增'
            this.open = true
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            // if( !row.configEditStatus ){
            //     this.$message({
            //         message: row.configEditRemark,
            //         type: 'error'
            //     })
            //     return;
            // }
            this.title = '修改'
            const machineJobProgressId = row.machineJobProgressId || this.ids
            getMachineJobProgress(machineJobProgressId).then(response => {
                let { data } = response
                data.amTypeName = `${data.amTypeName1}${data.amTypeName2}${data.amTypeName3}${data.amGradeName}`
                data.amTypeAll = [
                    data.amTypeCode1 ? data.amTypeCode1.toString() : "",
                    data.amTypeCode2 ? data.amTypeCode2.toString() : "",
                    data.amTypeCode3 ? data.amTypeCode3.toString() : "",
                    data.amGradeId ? data.amGradeId.toString() : ""
                ]
                data.machs = [
                    {
                        plotNo: data.plotNo,
                        plotName: data.plotName,
                        landType: data.landType,
                        raiseCropsCd: data.raiseCropsCd,
                        raiseCropsNm: data.raiseCropsNm,
                        prodProcessCode: data.prodProcessCode,
                        prodProcessName: data.prodProcessName,
                        linkCode: data.linkCode,
                        linkName: data.linkName,
                        workArea: data.workArea,
                        jobStartTime: data.jobStartTime,
                        jobEndTime: data.jobEndTime,
                        plotArea: data.plotArea,
                        machineToolModel: data.machineToolModel,
                    }
                ]
                this.form = data
                console.log(this.form.amTypeAll)
                this.open = true
                cropProcess({
                    raiseCrops: this.form.raiseCropsCd,
                    prodProcessCode: this.form.prodProcessCode,
                    statYear: this.form.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            })
        },
        /** 详情按钮操作 */
        handleShow(row) {
            this.title = '详情'
            const machineJobProgressId = row.machineJobProgressId || this.ids
            getMachineJobProgress(machineJobProgressId).then(response => {
                let { data } = response
                data.amTypeName = `${data.amTypeName1}${data.amTypeName2}${data.amTypeName3}${data.amGradeName}`
                data.amTypeAll = [
                    data.amTypeCode1 ? data.amTypeCode1.toString() : "",
                    data.amTypeCode2 ? data.amTypeCode2.toString() : "",
                    data.amTypeCode3 ? data.amTypeCode3.toString() : "",
                    data.amGradeId ? data.amGradeId.toString() : ""
                ]
                data.machs = [
                    {
                        plotNo: data.plotNo,
                        plotName: data.plotName,
                        landType: data.landType,
                        raiseCropsCd: data.raiseCropsCd,
                        raiseCropsNm: data.raiseCropsNm,
                        prodProcessCode: data.prodProcessCode,
                        prodProcessName: data.prodProcessName,
                        linkCode: data.linkCode,
                        linkName: data.linkName,
                        workArea: data.workArea,
                        jobStartTime: data.jobStartTime,
                        jobEndTime: data.jobEndTime,
                        plotArea: data.plotArea,
                        machineToolModel: data.machineToolModel,
                    }
                ]
                this.form = data
                this.open = true
                cropProcess({
                    raiseCrops: this.form.raiseCropsCd,
                    prodProcessCode: this.form.prodProcessCode,
                    statYear: this.form.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            })
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    let timeValidate = this.form.machs.every((item) => {
                        let workBeginTime = !!item.jobStartTime ? new Date(item.jobStartTime).getTime() : ''
                        let workEndTime = !!item.jobEndTime ? new Date(item.jobEndTime).getTime() : ''
                        return workBeginTime && workEndTime && workBeginTime <= workEndTime
                    })
                    if (!timeValidate) {
                        this.$message({
                            message: '\'作业结束时间\'需要晚于\'作业开始时间\'',
                            type: 'error'
                        })
                        return false
                    }
                    let timeValidate2 = this.form.machs.every((item) => {
                        let workBeginTime = !!item.jobStartTime ? new Date(item.jobStartTime).getTime() : ''
                        let workEndTime = !!item.jobEndTime ? new Date(item.jobEndTime).getTime() : ''
                        let yearBegin = new Date(workBeginTime).getFullYear()//开始时间年份等于当前选择的年份
                        let yearEnd = new Date(workEndTime).getFullYear()
                        return yearBegin == this.form.year && yearEnd == this.form.year
                    })
                    if (!timeValidate2) {
                        this.$message({
                            message: '\'作业开始/结束时间\'，必须为当前作业年份',
                            type: 'error'
                        })
                        return false
                    }

                    if (!this.form.amTypeName1 && this.form.amTypeCode1) {
                        this.machineList.forEach((v, i) => {
                            if (this.form.amTypeCode1 == v.amTypeCode) {
                                this.form.amTypeName1 = v.amTypeName;
                                if (v.children) {
                                    v.children.forEach((v2, i2) => {
                                        if (this.form.amTypeCode2 == v2.amTypeCode) {
                                            this.form.amTypeName2 = v2.amTypeName;
                                            if (v2.children) {
                                                v2.children.forEach((v3, i) => {
                                                    if (this.form.amTypeCode3 == v3.amTypeCode) {
                                                        this.form.amTypeName3 = v3.amTypeName;
                                                        if (v3.children) {
                                                            v3.children.forEach((v4, i) => {
                                                                if (this.form.amGradeId == v4.amTypeCode) {
                                                                    this.form.amGradeName = v4.amTypeName;
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            }
                                        }
                                    });
                                }
                            }
                        });
                    }

                    if (this.form.companyId && !this.form.companyName) {
                        let cInfo = this.amcompanyList.filter(v => v.companyId == this.form.companyId);
                        this.form.companyName = cInfo[0] ? cInfo[0].companyName : "";
                    }

                    let obj = {
                        year: this.form.year,
                        orgCode: this.form.orgCode,
                        machineId: this.formmachineOriginType ? this.form.machineId : "",
                        licenseNo: this.form.licenseNo,
                        amOwnerName: this.form.amOwnerName,
                        amModelName: this.form.amModelName,
                        amTypeCode1: this.form.amTypeCode1,
                        amTypeCode2: this.form.amTypeCode2,
                        amTypeCode3: this.form.amTypeCode3,
                        amGradeId: this.form.amGradeId,
                        amTypeName1: this.form.amTypeName1,
                        amTypeName2: this.form.amTypeName2,
                        amTypeName3: this.form.amTypeName3,
                        amGradeName: this.form.amGradeName,
                        companyId: this.form.companyId,
                        companyName: this.form.companyName,
                        machineOriginType: this.form.machineOriginType
                    };
                    if (this.form.machineJobProgressId != null) {
                        let data = this.form.machs[0]
                        obj.machineJobProgressId = this.form.machineJobProgressId
                        obj.plotNo = data.plotNo
                        obj.plotName = data.plotName
                        obj.landType = data.landType
                        obj.raiseCropsCd = data.raiseCropsCd
                        obj.raiseCropsNm = data.raiseCropsNm
                        obj.prodProcessCode = data.prodProcessCode
                        obj.prodProcessName = data.prodProcessName
                        obj.linkCode = data.linkCode
                        obj.linkName = data.linkName
                        obj.workArea = data.workArea
                        obj.jobStartTime = new Date(data.jobStartTime).getTime()
                        obj.jobEndTime = new Date(data.jobEndTime).getTime()
                        obj.plotArea = data.plotArea
                        obj.machineToolModel = data.machineToolModel

                        updateMachineJobProgress(obj).then(response => {
                            this.$modal.msgSuccess('修改成功')
                            this.open = false
                            this.getList()
                            // if( response.code == -1 ){
                            //     this.$message({
                            //         message: '\'作业开始/结束时间\'，必须为当前作业年份',
                            //         type: 'error'
                            //     })
                            // }
                        })
                    } else {
                        obj.machineJobPlotList = this.form.machs;
                        addMachineJobProgress(obj).then(response => {
                            this.$modal.msgSuccess('新增成功')
                            this.open = false
                            this.getList()
                        })
                    }
                }
            })
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            if (!row.configEditStatus) {
                this.$message({
                    message: row.configEditRemark,
                    type: 'error'
                })
                return;
            }
            const machineJobProgressIds = row.machineJobProgressId || this.ids
            this.$confirm('是否确认删除该数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                return Array.isArray(machineJobProgressIds) ? delMachineJobProgresss(machineJobProgressIds) : delMachineJobProgress(machineJobProgressIds)
            }).then(() => {
                this.getList()
                this.$modal.msgSuccess('删除成功')
            })
        },
        exportTemplate() {
            window.location.href = exportTemplate()
        },
        //组织机构下拉
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
        },
        //组织机构下拉
        handleOrgChange2({orgCode}) {
            this.form.orgCode = orgCode
        },
        machineOriginTypeChange(val) {
            //车辆类型修改
            this.form.licenseNo = "";
            this.form.amOwnerName = "";
            this.form.amModelName = "";
            this.form.amTypeCode1 = "";
            this.form.amTypeCode2 = "";
            this.form.amTypeCode3 = "";
            this.form.amGradeId = "";
            this.form.amTypeName1 = "";
            this.form.amTypeName2 = "";
            this.form.amTypeName3 = "";
            this.form.amGradeName = "";
            this.form.companyId = "";
            this.form.companyName = "";
            this.form.amTypeName = "";
            this.form.amTypeAll = [];
        },
        //上报配置 弹窗保存
        saveConfig() {
            this.$refs['configForm'].validate(valid => {
                if (valid) {
                    updateConfig({
                        useStatus: this.configForm.useStatus,
                        jobLimitDay: this.configForm.jobLimitDay,
                        jobLimitHour: this.configForm.jobLimitHour
                    }).then(response => {
                        this.$modal.msgSuccess('保存成功')
                        this.showConfig = false
                        this.getList()
                    })
                }
            });
        },
        //上报配置 弹窗关闭
        openConfig() {
            infoConfig().then(response => {
                this.configForm = response.data;
                this.showConfig = true
            });
        },
        //上报配置 弹窗关闭
        closeConfig() {
            this.showConfig = false
        }
    }
}

</script>
<style scoped lang="scss">
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';

.right {
    float: right;
    font-size: 14px;
    padding-top: 4px;
}

.colorblue {
    color: blue;
}

:deep(.upload-file-uploader) {
    margin-bottom: 0;
}

.select-btn {
    height: 35px;
    position: absolute;
    right: 11px;
    bottom: 12px
}

.dialog-tab {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-weight: bold;
    height: 40px;
    background: #e6f0fc;
    line-height: 40px;
    margin: 20px 0;
    padding: 0 15px;
}

:deep(.form-style .el-form-item) {
    margin-right: 50px;
    width: 100%;

    .el-date-editor {
        width: 100% !important;
    }
}

:deep(.is-center.el-dialog) {
    margin-top: 30vh !important;
}

:deep(.form-style .el-form-item .el-form-item__label) {
    text-align: left;
}

:deep(.form-style .el-form-item) {
    display: flex;
    flex-direction: column;
    text-align: left;
}

:deep(.el-tag.el-tag--info) {
    background-color: transparent;
    border: none;
    color: #606266;
    padding-left: 0;
    font-size: 14px;
    padding-right: 0;

    .el-tag__close {
        display: none;
    }
}

:deep(.el-dialog__body .el-tag--info+.el-tag--info) {
    margin-left: 0;

    &::before {
        content: ',';
    }
}

.plot-dia {
    .el-radio {
        display: inline-block;
    }
}
</style>
