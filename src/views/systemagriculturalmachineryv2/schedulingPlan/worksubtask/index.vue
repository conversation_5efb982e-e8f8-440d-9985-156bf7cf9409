<!--
@name:作业记录-统管
@description:
@author：<PERSON> ling
@time：2023/4/5
-->
<template>
    <div class="app-container">
        <el-form
            ref="queryForm"
            :model="queryParams"
            label-width="90px"
            class="form-line"
            v-show="showSearch"
        >
            <el-row :gutter="30">
                <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                        <el-select
                            v-model="queryParams.year"
                            placeholder="请选择年份"

                            @change="lockFlag"
                            clearable
                        >
                            <el-option
                                v-for="dict in yearNoOptions"
                                :key="dict.code"
                                :label="dict.name"
                                :value="dict.code"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()"  style="width: 100%;"
                            :defaultOrgCode="queryParams.orgCode"
                            :placeholderText="'请选择归属'"
                            @handleOrgCode="handleOrgChange"
                        ></OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select
                            v-model="queryParams.raiseCropsCd"
                            clearable
                            placeholder="请选择种植作物"

                        >
                            <el-option
                                v-for="dict in raiseCropsCdOptions"
                                :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm"
                                :value="dict.raiseCropsCd"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="生产流程" prop="prodProcessCode">
                        <el-select
                            v-model="queryParams.prodProcessCode"
                            clearable
                            placeholder="请选择生产流程"

                        >
                            <el-option
                                v-for="dict in prodProcessOptions"
                                :key="dict.code"
                                :label="dict.name"
                                :value="dict.code"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select
                            v-model="queryParams.linkCode"
                            clearable
                            placeholder="请选择作业环节"

                        >
                            <el-option
                                v-for="dict in linkCodeOptions"
                                :key="dict.linkCode"
                                :label="dict.linkName"
                                :value="dict.linkCode"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="地块名称" prop="plotName">
                        <el-input
                            v-model="queryParams.plotName"
                            placeholder="请填写地块名称"
                            clearable

                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="牌照号" prop="licenseNo">
                        <el-input
                            v-model="queryParams.licenseNo"
                            placeholder="请填写牌照号"
                            clearable

                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="机主姓名" prop="amOwnerName">
                        <el-input
                            v-model="queryParams.amOwnerName"
                            placeholder="请填写机主姓名"
                            clearable

                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="驾驶员姓名" prop="driverName">
                        <el-input
                            v-model="queryParams.driverName"
                            placeholder="请填写驾驶员姓名"
                            clearable

                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="农机分类" prop="amTypeAll">
                        <el-cascader
                            @change="amTypeAllChange"
                            style="width: 100%;"
                            ref="orgSelect"
                            v-model="amTypeAll"
                            :options="machineList"
                            placeholder="请选择农机分类"
                            :props="{
                                label: 'amTypeName',
                                value: 'amTypeCode',
                                expandTrigger: 'hover',
                                multiple: false,
                                checkStrictly: true,
                            }"
                            :show-all-levels="false"
                            collapse-tags
                            clearable

                        ></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="text-right" align="right">
                    <el-button
                        icon="Refresh"

                        @click="resetQuery"
                    >重置
                    </el-button
                    >
                    <el-button
                        type="primary"
                        icon="Search"

                        @click="handleQuery"
                    >搜索
                    </el-button
                    >
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5" v-if="isLock">
                <el-button
                    type="primary"
                    icon="Plus"

                    @click="handleAdd"
                    v-hasPermi="['worksubtask:insert']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5" v-if="isLock">
                <FileUpload1
                    :isShowTip="false"
                    :default="['xls']"
                    :text1="'导入'"
                    :uploadFileUrl="uploadFileUrl"
                    @newlist="getList(1)"
                    v-hasPermi="['worksubtask:importExcel']"
                ></FileUpload1>
            </el-col>
            <el-col :span="1.5" v-if="isLock">
                <el-button

                    plain
                    icon="Download"

                    @click="exportTemplate"
                    v-hasPermi="['worksubtask:exportTemplate']"
                >模板下载
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <!--导出excel-->
                <exceldownload
                    ref="exceldownload"
                    :exceltext="'导出'"
                    icon="Download"
                    :param="queryParams"
                    :url="url"
                    v-hasPermi="['worksubtask:exportExcel']"
                ></exceldownload>
            </el-col>
            <div style="display: flex;justify-content: flex-end;align-items: center;font-size: 14px;margin-left: auto;">
                <el-col :span="1.5" class="" style="margin-bottom: 0;">
                    计划面积：<span class="colorblue">{{ planAreaCount }}(万亩)</span>
                </el-col>
                <el-col :span="1.5" class="" style="margin-bottom: 0;">
                    作业面积：<span class="colorblue">{{ workAreaCount }}(万亩)</span>
                </el-col>
                <el-col :span="1.5" class="" style="margin-bottom: 0;">
                    完成率：<span class="colorblue">{{ finalizePart }}%</span>
                </el-col>
                <right-toolbar
                style="float: right;"

                :columns="columns"
                v-model:showSearch="showSearch"
                @queryTable="getList"
                ></right-toolbar>
            </div>
        </el-row>

        <el-table ref="tableRef" :data="worksubtaskList" :span-method="objectSpanMethod"
                  :height="tableHeight"
        >
            <template v-for="item in columns">
                <el-table-column v-bind="item" :key="item.key" v-if="item.visible">
                     <template #default="scope">
                        <div v-if="item.key === 'amTypeName'">
                            {{
                                scope.row.amTypeName1 ? scope.row.amTypeName1 + '/' : ''
                            }}{{
                                scope.row.amTypeName2 ? scope.row.amTypeName2 + '/' : ''
                            }}{{ scope.row.amTypeName3 ? scope.row.amTypeName3 + '/' : '' }}{{ scope.row.amGradeName }}
                        </div>
                        <div v-else-if="item.key === 'inTime'">
                            {{ parseTime(scope.row.inTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                        </div>
                        <div v-else-if="item.key === 'familyFarmLeaderList'">
                            <div v-for="(item,index) in scope.row.familyFarmLeaderList">{{ item.familyFarmLeaderName +" "+ item.familyFarmLeaderPhone + (index < (scope.row.familyFarmLeaderList.length -1) ? ",":"")}}</div>
                        </div>
                        <div v-else>
                            {{ scope.row[item.key] }}
                        </div>
                    </template>
                </el-table-column>
            </template>
            <!-- <el-table-column label="归属" align="center" prop="orgName" width="180"/>
            <el-table-column label="年份" align="center" prop="year"/>
            <el-table-column label="地块名称" align="center" prop="plotNameAndArea"/>
            <el-table-column label="种植作物" align="center" prop="raiseCropsNm"/>
            <el-table-column label="生产流程" align="center" prop="prodProcessName"/>
            <el-table-column label="作业环节" align="center" prop="linkName" width="120"/>
            <el-table-column label="牌照号" align="center" prop="licenseNo" width="120"/>
            <el-table-column label="机主" align="center" prop="amOwnerPhone" width="100"/>
            <el-table-column label="驾驶员" align="center" prop="driverName" width="120"/>

            <el-table-column label="农机分类" align="center" prop="" width="180">
                 <template #default="scope">
                    {{
                        scope.row.amTypeName1 ? scope.row.amTypeName1 + '/' : ''
                    }}{{
                        scope.row.amTypeName2 ? scope.row.amTypeName2 + '/' : ''
                    }}{{ scope.row.amTypeName3 ? scope.row.amTypeName3 + '/' : '' }}{{ scope.row.amGradeName }}
                </template>
            </el-table-column>
            <el-table-column label="分配面积" align="center" prop="allocatedArea"/>

            <el-table-column label="作业面积" align="center" prop="workArea"/>
            <el-table-column label="合格面积" align="center" prop="passArea"/>

            <el-table-column label="作业开始时间" align="center" prop="inTime" width="180">
                 <template #default="scope">
                    <span>{{ parseTime(scope.row.inTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="作业结束时间" align="center" prop="outTime" width="180">

            </el-table-column> -->

            <el-table-column label="操作" align="center" fixed="right" width="150">
                 <template #default="scope">
                    <el-button
                        link type="primary"
                        icon="Search"
                        @click="handleShow(scope.row)"
                        v-hasPermi="['worksubtask:info']"
                    >详情
                    </el-button>
                    <!--                    <el-button-->
                    <!--                        -->
                    <!--                        link type="primary"-->
                    <!--                        icon="Edit"-->
                    <!--                        @click="handleUpdate(scope.row)"-->
                    <!--                        v-hasPermi="['worksubtask:update']"-->
                    <!--                    >修改-->
                    <!--                    </el-button>-->
                    <el-button

                        link type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['worksubtask:delete']"
                        v-if="isLock"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
             v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        />

        <!-- 添加或修改作业记录（统管）对话框 -->
        <el-dialog :title="title"  v-model="open"width="65%" append-to-body @closed="cancel"
                   :close-on-click-modal="false"
        >
            <el-form ref="form" :model="form" label-width="120px" class="form-style form-100" label-position="top">

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="归属单位" prop="orgCode" required :rules="rules.orgCode">
                            <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()"  style="width: 100%;"
                                :defaultOrgCode="form.orgCode"
                                :placeholderText="'请选择归属'"
                                @handleOrgCode="handleOrgChange2"
                            ></OrgSelects>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业年份" prop="year" required :rules="rules.year">
                            <el-select v-model="form.year" placeholder="请选择年份" style="width: 100%"
                                       @change="clearPlot"
                            >
                                <el-option
                                    v-for="dict in yearNoOptions"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" style="position: relative">
                        <el-form-item label="地块名称" prop="plotName" required :rules="rules.plotName">
                            <el-input v-model="form.plotName" placeholder="请选择地块" disabled />
                        </el-form-item>
                        <el-button type="primary" class="select-btn"
                                   @click="openSelecteFormPlot"
                        > 选择
                        </el-button>
                    </el-col>

                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="" required>
                            <el-input v-model="form.raiseCropsNm" placeholder="选择带入" disabled />
                            <!--                            <el-select v-model="form.raiseCropsCd" clearable placeholder="请选择种植作物"-->
                            <!--                                       @change="raiseCropsCdChange($event, true)"-->
                            <!--                                       style="width: 100%">-->
                            <!--                                <el-option-->
                            <!--                                    v-for="(dict, index) in raiseCropsCdOptions"-->
                            <!--                                    :key="index"-->
                            <!--                                    :label="dict.name"-->
                            <!--                                    :value="dict.code"-->
                            <!--                                />-->
                            <!--                            </el-select>-->
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="生产流程" prop="" required>
                            <el-input v-model="form.prodProcessName" placeholder="选择带入" disabled />
                            <!--                            <el-select v-model="form.prodProcessCode" clearable placeholder="请选择生产流程"-->
                            <!--                                       @change="prodProcessCodeChange($event, true)"-->
                            <!--                                       style="width: 100%">-->
                            <!--                                <el-option-->
                            <!--                                    v-for="(dict, index) in prodProcessOptions"-->
                            <!--                                    :key="index"-->
                            <!--                                    :label="dict.name"-->
                            <!--                                    :value="dict.code"-->
                            <!--                                />-->
                            <!--                            </el-select>-->
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业环节" prop="" required>
                            <el-input v-model="form.linkName" placeholder="选择带入" disabled />
                            <!--                            <el-select v-model="form.linkCode" clearable placeholder="请选择作业环节"-->
                            <!--                                       style="width: 100%">-->
                            <!--                                <el-option-->
                            <!--                                    v-for="(dict, index) in linkCodeOptions"-->
                            <!--                                    :key="index"-->
                            <!--                                    :label="dict.linkName"-->
                            <!--                                    :value="dict.linkCode"-->
                            <!--                                />-->
                            <!--                            </el-select>-->
                        </el-form-item>
                    </el-col>
                </el-row>
                <div v-for="(item, index) in form.machs" :key="index">
                    <el-row style="margin-top: 20px" :gutter="20">
                        <el-col :span="24">
                            <div class="dialog-tab">
                                <span>作业农机{{ index + 1 }}</span>
                                <div>
                                    <el-button type="primary"  @click="addMachine" v-if="index == 0">新增
                                    </el-button>
                                    <el-button type="primary"  @click="popMachine(index)"
                                               v-if="form.machs&&form.machs.length > 1"
                                    >删除
                                    </el-button>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8" style="position: relative">
                            <el-form-item label="牌照号" :prop="'machs.'+index+'.licenseNo'"
                                          :rules="getLicenseNoRules(item)"
                            >
                                <el-input v-model="item.licenseNo" placeholder="请选择农机" disabled />
                            </el-form-item>
                            <el-button type="primary" class="select-btn"
                                       @click="openSelecteForm(index)"
                            > 选择
                            </el-button>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="农机分类" required>
                                <el-input
                                    :value="`${item.amTypeName || ''}`"
                                    placeholder="选择带入" disabled
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="机主" required>
                                <el-input v-model="item.amOwnerName" placeholder="选择带入" disabled />
                            </el-form-item>
                        </el-col>


                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="驾驶员" required :rules="rules.driverId"
                                          :prop="'machs.'+index+'.driverId'"
                            >
                                <!--                                <el-input v-model="item.driverName" placeholder="选择带入" disabled/>-->
                                <el-select v-model="item.driverId" clearable placeholder="请选择驾驶员"
                                           @change="driversChange($event,index)"
                                >
                                    <el-option
                                        v-for="dict in item.drivers"
                                        :key="dict.driverId"
                                        :label="dict.driverName+ ' ' + (dict.phoneNo ? dict.phoneNo : '') "
                                        :value="dict.driverId"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="分配面积(亩)" :rules="rules.allocatedArea"
                                          :prop="'machs.'+index+'.allocatedArea'"
                            >
                                <el-input v-model="item.allocatedArea" placeholder="请输入分配面积(亩)" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="作业面积(亩)" :rules="rules.workArea"
                                          :prop="'machs.'+index+'.workArea'"
                            >
                                <el-input v-model="item.workArea" placeholder="请输入作业面积(亩)" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="合格面积(亩)" :rules="rules.passArea"
                                          :prop="'machs.'+index+'.passArea'"
                            >
                                <el-input v-model="item.passArea" placeholder="请输入合格面积(亩)" />
                            </el-form-item>
                        </el-col>


                        <el-col :span="8">
                            <el-form-item label="作业开始时间"
                                          :prop="'machs.'+index+'.inTime'"
                                          :rules="rules.inTime"
                            >
                                <el-date-picker
                                    style="width: 100%;"
                                    v-model="item.inTime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="请选择作业开始时间"
                                    type="datetime"
                                    clearable
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="作业结束时间"
                                          :prop="'machs.'+index+'.outTime'"
                                          :rules="rules.outTime"
                            >
                                <el-date-picker
                                    v-model="item.outTime"
                                    style="width: 100%;"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="请选择作业结束时间"
                                    type="datetime"
                                    clearable
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="作业单价(元/亩)" :prop="'machs.'+index+'.workUnitCost'"  :rules="rules.workUnitCost">
                                <el-input
                                    v-model="item.workUnitCost"
                                    placeholder="请输入作业单价"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                        <el-form-item label="规模家庭农场场长" :prop="'machs.'+index+'.familyFarmLeaderList'"  :title="formatFarmLeaderList(item.familyFarmLeaderList)">
                            <el-select  multiple value-key="familyFarmLeaderIdNumber"  v-model="item.familyFarmLeaderList" placeholder="请选择规模家庭农场场长" clearable style="width: 100%;">
                                <el-option v-for="dict in familyFarmLeaderList" :key="dict.familyFarmLeaderIdNumber" :label="dict.familyFarmLeaderName" :value="dict">
                                    <span style="float: left">{{ dict.familyFarmLeaderName }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.familyFarmLeaderPhone }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    </el-row>
                </div>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </template>
        </el-dialog>

        <el-dialog title="详情" v-model="openInfo" width="65%" append-to-body custom-class="is-center"
                   @close="reset"
        >
            <el-descriptions title="" :column="3" :labelStyle="{fontWeight:'bold'}">
                <el-descriptions-item label="归属单位">{{ form.orgName }}</el-descriptions-item>
                <el-descriptions-item label="作业年份">{{ form.year }}</el-descriptions-item>
                <el-descriptions-item label="地块名称">{{ form.plotName }}</el-descriptions-item>
                <el-descriptions-item label="种植作物">{{ form.raiseCropsNm }}</el-descriptions-item>
                <el-descriptions-item label="生产流程">{{ form.prodProcessName }}</el-descriptions-item>
                <el-descriptions-item label="作业环节">{{ form.linkName }}</el-descriptions-item>
            </el-descriptions>
            <div v-for="(item, index) in form.machs" :key="index">
                <div class="dialog-tab">
                    <span>作业农机{{ index + 1 }}</span>
                </div>
                <el-descriptions title="" :column="3" :labelStyle="{fontWeight:'bold'}">
                    <el-descriptions-item label="牌照号">{{ item.licenseNo }}</el-descriptions-item>
                    <el-descriptions-item label="农机分类">
                        {{ item.amTypeName1 ? item.amTypeName1 + '/' : ''
                        }}{{ item.amTypeName2 ? item.amTypeName2 + '/' : ''
                        }}{{ item.amTypeName3 ? item.amTypeName3 + '/' : '' }}{{ item.amGradeName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="机主">{{ item.amOwnerName }}</el-descriptions-item>
                    <el-descriptions-item label="驾驶员">{{ item.driverName }}</el-descriptions-item>
                    <el-descriptions-item label="分配面积(亩)">{{ item.allocatedArea }}</el-descriptions-item>
                    <el-descriptions-item label="作业面积(亩)">{{ item.workArea }}</el-descriptions-item>
                    <el-descriptions-item label="合格面积(亩)">{{ item.passArea }}</el-descriptions-item>
                    <el-descriptions-item label="作业开始时间">{{ item.inTime }}</el-descriptions-item>
                    <el-descriptions-item label="作业结束时间">{{ item.outTime }}</el-descriptions-item>
                    <el-descriptions-item label="作业单价(元/亩)">{{ item.workUnitCost }}</el-descriptions-item>
                    <el-descriptions-item label="规模家庭农场场长">
                        <div v-for="(it,index) in item.familyFarmLeaderList">{{ it.familyFarmLeaderName +" "+ it.familyFarmLeaderPhone + (index < (item.familyFarmLeaderList.length -1) ? ",":"")}}</div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-dialog>


        <!--   地块     -->
        <el-dialog v-model="showPlot" title="选择地块" width="900px" @close="closePlot">
            <el-form ref="plotForm" :model="plotParams" label-width="120px">
                <el-row>
                    <!--                    <el-col :span="8">-->
                    <!--                        <el-form-item label="所属年份" prop="statYear" required :rules="rules.statYear">-->
                    <!--                            <el-select v-model="plotParams.statYear" placeholder="请选择作业年份"-->
                    <!--                                       style="width: 100%">-->
                    <!--                                <el-option-->
                    <!--                                    v-for="dict in yearNoOptions"-->
                    <!--                                    :key="dict.code"-->
                    <!--                                    :label="dict.name"-->
                    <!--                                    :value="dict.code"-->
                    <!--                                />-->
                    <!--                            </el-select>-->
                    <!--                        </el-form-item>-->
                    <!--                    </el-col>-->
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="raiseCropsCd" required :rules="rules.raiseCropsCd">
                            <el-select v-model="plotParams.raiseCropsCd" clearable placeholder="请选择种植作物"

                            >
                                <el-option
                                    v-for="dict in raiseCropsCdOptionsPlot"
                                    :key="dict.raiseCropsCd"
                                    :label="dict.raiseCropsNm"
                                    :value="dict.raiseCropsCd"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="生产流程" prop="prodProcessCode" required :rules="rules.prodProcessCode">
                            <el-select v-model="plotParams.prodProcessCode" clearable placeholder="请选择生产流程"

                            >
                                <el-option
                                    v-for="dict in prodProcessOptions"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="作业环节" prop="linkCode" required :rules="rules.linkCode">
                            <el-select v-model="plotParams.linkCode" clearable placeholder="请选择作业环节"
                            >
                                <el-option
                                    v-for="dict in linkCodeOptionsPlot"
                                    :key="dict.linkCode"
                                    :label="dict.linkName"
                                    :value="dict.linkCode"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="地块名称" prop="plotName">
                            <el-input v-model="plotParams.plotName" @clear="handleCheckedChange"
                                      @input="handleCheckedChange" placeholder="请填写地块名称"
                            >
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <!--                    <el-col :span="7" :offset="1">-->
                    <!--                        <el-button type="success" icon="Search"  @click="selectPlot()">搜索</el-button>-->
                    <!--                        <el-button icon="Refresh" type="info"  @click="resetQueryPlot">重置</el-button>-->
                    <!--                    </el-col>-->
                </el-row>
            </el-form>
            <div style="max-height: 600px;overflow-y: scroll">
                <el-radio-group v-model="checkboxGroup" class="plot-dia">
                    <el-radio v-for="(item,index) in plotListFilter" :key="index" :label="item.plotNo" border
                              style="width: 276px;margin:5px;display: flex;align-items: center;"
                    >{{ item.plotName }}{{ item.pastPlotName == null ? '' : '(' + item.pastPlotName + ')' }}
                    </el-radio>
                </el-radio-group>
                <div v-if="!plotListFilter || plotListFilter.length ==0" style="text-align: center">暂无相关地块</div>
            </div>
            <template #footer class="dialog-footer">
                <el-button type="primary" @click="handleApply">确 定</el-button>
                <el-button @click="closePlot">取 消</el-button>
            </template>
        </el-dialog>


        <!--   修改-绑定农机     -->
        <bindDialog
            @changeForm="selectForm"
            v-if="showMachine"
            :is-edit="showMachine"
            v-model:value="showMachine"
            isCheckLicenseNo
            :amMachineFlag="1"
        />

    </div>
</template>

<script>
import {
    addWorksubtask,
    delWorksubtask,
    delWorksubtasks,
    exportTemplate,
    getLockFlag,
    getWorksubtask,
    listWorksubtask,
    queryDriverByMachId,
    tipsWorksubtask,
    updateWorksubtask
} from '@/api/systemagriculturalmachineryv2/schedulingPlan/worksubtask'
import { selectDictLabelForRaiseCrops,selectDictLabelForLink } from "@/utils/cop";
import { queryResPlotInfo } from '@/api/systemagriculturalmachineryv2/workApply'
import { queryDictTree } from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'

import { cropProcess } from '@/api/systemagriculturalmachineryv2/dispatch/operational'
import { getDicts,getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import FileUpload1 from '@/views/systemagriculturalmachineryv2/components/FileUpload1'
import exceldownload from '@/views/systemagriculturalmachineryv2/components/exceldownload'
import bindDialog from '../../basicInfo/registerRelation/bindDialog'
import { getLicenseNoRules } from '@/views/systemagriculturalmachineryv2/utils/machine'
import {familyFarmLeader} from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'
import useUserStore from "@/store/modules/user"
import { selectDictLabel } from '@/utils/cop'
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
import {orgDictsUrl} from "@/api/systemagriculturalmachineryv2/orgDict.js";
export default {
    name: '/schedulingPlan/worksubtask/querySupplePage',
    components: {
        FileUpload1, exceldownload, bindDialog
    },
    mixins: [calcTabelHeight],
    data() {
        var passAreaValidateNum = (rule, value, callback) => {
            var reg = /^[0-9]+(\.?[0-9]{1,4})?$/
            if (value === null || value === '' || typeof (value) == 'undefined') {
                callback(new Error('请输入合格面积（亩）'))
            } else if (value.toString().split('.')[0].length > 6) {
                callback(new Error('数字太大，请确认'))
            } else if (reg.test(value)) {
                callback()
            } else {
                callback(new Error('请输入正数(最多带四位小数)'))
            }
        }
        var allocatedAreaValidateNum = (rule, value, callback) => {
            var reg = /^[0-9]+(\.?[0-9]{1,4})?$/
            if (value === null || value === '' || typeof (value) == 'undefined') {
                callback(new Error('请输入分配面积（亩）'))
            } else if (value.toString().split('.')[0].length > 6) {
                callback(new Error('数字太大，请确认'))
            } else if (reg.test(value)) {
                callback()
            } else {
                callback(new Error('请输入正数(最多带四位小数)'))
            }
        }
        var workAreaValidateNum = (rule, value, callback) => {
            var reg = /^[0-9]+(\.?[0-9]{1,4})?$/
            if (value === null || value === '' || typeof (value) == 'undefined') {
                callback(new Error('请输入作业面积（亩）'))
            } else if (value.toString().split('.')[0].length > 6) {
                callback(new Error('数字太大，请确认'))
            } else if (reg.test(value)) {
                callback()
            } else {
                callback(new Error('请输入正数(最多带四位小数)'))
            }
        }
        var workUnitCost = (rule, value, callback) => {
            var reg = /^[0-9]+(\.?[0-9]{1,2})?$/;
            if (value === null || value === "" || typeof value == "undefined") {
                callback();
            } else if (value.toString().split(".")[0].length > 7) {
                callback(new Error("作业单价不得超过100w，请确认"));
            } else if (reg.test(value)) {
                if(value.toString() === '0'){
                    callback(new Error("作业单价请输入正数(最多带两位小数)"));
                }else{
                    callback();
                }
            } else {
                callback(new Error("请输入正数(最多带两位小数)"));
            }
        };
        return {
            familyFarmLeaderList:[],
            columns: [
                {
                    align: 'center',
                    visible: true,
                    label: '归属',
                    key: 'orgName',
                    prop: 'orgName',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: '年份',
                    key: 'year',
                    prop: 'year'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '地块名称',
                    key: 'plotNameAndArea',
                    prop: 'plotNameAndArea'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '种植作物',
                    key: 'raiseCropsNm',
                    prop: 'raiseCropsNm'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '生产流程',
                    key: 'prodProcessName',
                    prop: 'prodProcessName'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '作业环节',
                    key: 'linkName',
                    prop: 'linkName',
                    width: 120
                },
                {
                    align: 'center',
                    visible: true,
                    label: '牌照号',
                    key: 'licenseNo',
                    prop: 'licenseNo',
                    width: 120
                },
                {
                    align: 'center',
                    visible: true,
                    label: '机主',
                    key: 'amOwnerPhone',
                    prop: 'amOwnerPhone',
                    width: 100
                },
                {
                    align: 'center',
                    visible: true,
                    label: '驾驶员',
                    key: 'driverName',
                    prop: 'driverName',
                    width: 120
                },
                {
                    align: 'center',
                    visible: true,
                    label: '农机分类',
                    key: 'amTypeName',
                    prop: 'amTypeName',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: '分配面积(亩)',
                    key: 'allocatedArea',
                    prop: 'allocatedArea'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '作业面积(亩)',
                    key: 'workArea',
                    prop: 'workArea'
                },
                {
                    align: 'center',
                    visible: true,
                    label: '合格面积(亩)',
                    key: 'passArea',
                    prop: 'passArea',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: '作业开始时间',
                    key: 'inTime',
                    prop: 'inTime',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: '作业结束时间',
                    key: 'outTime',
                    prop: 'outTime',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: "作业单价(元/亩)",
                    key: 'workUnitCost',
                    prop: 'workUnitCost',
                    width: 180
                },
                {
                    align: 'center',
                    visible: true,
                    label: "规模家庭农场场长",
                    key: 'familyFarmLeaderList',
                    prop: 'familyFarmLeaderList',
                    width: 180
                },

            ],
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 作业补录（统管）表格数据
            worksubtaskList: [],
            spanArr: [],
            pos: null,
            openInfo: false,
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                page: 1,
                rows: 10,
                year: String(new Date().getFullYear()),
                orgCode: useUserStore().currentOrgCode,
                orgName: null,
                raiseCropsCd: null,
                raiseCropsNm: null,
                prodProcessCode: null,
                prodProcessName: null,
                linkCode: null,
                linkName: null,
                plotName: null,
                licenseNo: null,
                amOwnerName: null,
                driverName: null
            },
            // 表单参数
            form: {
                year: '',
                orgCode: '',
                plotNo: '',
                plotName: '',
                landType: '',
                plotArea: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: '',
                raiseCropsNm: '',
                prodProcessName: '',
                linkName: '',
                machs: [{
                    driver: '',
                    drivers: [],//驾驶员列表，提交时记得删除
                    licenseNo: '',
                    machineId: '',
                    amOwnerId: '',
                    amOwnerName: '',
                    ownerCertNo: '',
                    inTime: '',
                    outTime: '',
                    amTypeCode1: '',
                    amTypeName1: '',
                    amTypeCode2: '',
                    amTypeName2: '',
                    amTypeCode3: '',
                    amTypeName3: '',
                    amGradeId: '',
                    amGradeName: '',
                    amModelId: '',
                    amModelName: '',
                    driverId: '',
                    amOwnerPhone: '',
                    driverName: '',
                    allocatedArea: '',
                    passArea: '',
                    workArea: '',
                    workUnitCost:'',
                    familyFarmLeaderList:[]
                }]
            },
            machsIndex: null,
            machs: {
                licenseNo: '',
                machineId: '',
                amOwnerId: '',
                amOwnerName: '',
                ownerCertNo: '',
                inTime: '',
                outTime: '',
                amTypeCode1: '',
                amTypeName1: '',
                amTypeCode2: '',
                amTypeName2: '',
                amTypeCode3: '',
                amTypeName3: '',
                amGradeId: '',
                amGradeName: '',
                amModelId: '',
                amModelName: '',
                driverId: '',
                amOwnerPhone: '',
                driverName: '',
                allocatedArea: '',
                passArea: '',
                workArea: '',
                workUnitCost:'',
                familyFarmLeaderList:[]
            },
            // 表单校验
            rules: {
                year: [
                    {
                        required: true,
                        message: '请选择作业年份',
                        trigger: 'change'
                    }
                ],
                statYear: [
                    {
                        required: true,
                        message: '请选择所属年份',
                        trigger: 'change'
                    }
                ],
                orgCode: [
                    {
                        required: true,
                        message: '请选择归属单位',
                        trigger: 'change'
                    }
                ],
                plotName: [
                    {
                        required: true,
                        message: '请选择地块',
                        trigger: 'change'
                    }
                ],
                raiseCropsCd: [
                    {
                        required: true,
                        message: '请选择种植作物',
                        trigger: 'change'
                    }
                ],
                prodProcessCode: [
                    {
                        required: true,
                        message: '请选择生产流程',
                        trigger: 'change'
                    }
                ],
                linkCode: [
                    {
                        required: true,
                        message: '请选择作业环节',
                        trigger: 'change'
                    }
                ],
                workUnitCost: [
                    {
                        required: false,
                        validator: workUnitCost,
                        trigger: 'blur'
                    }
                ],

                driverId: [{ required: true, trigger: 'change', message: '请选择驾驶员' }],
                licenseNo: [{ required: true, trigger: 'change', message: '请选择农机' }],
                inTime: [{ required: true, trigger: 'change', message: '请选择作业开始时间' }],
                outTime: [{ required: true, trigger: 'change', message: '请选择作业结束时间' }],
                passArea: [{ required: true, validator: passAreaValidateNum, trigger: 'blur' }],
                allocatedArea: [{ required: true, validator: allocatedAreaValidateNum, trigger: 'blur' }],
                workArea: [{
                    required: true,
                    validator: workAreaValidateNum,
                    trigger: 'blur'
                }]

            },
            url: window.VITE_APP_BASE_API + '/'+import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE+'/schedulingPlan/worksubtask/exportSuppleExcel',
            //导入地址
            uploadFileUrl: window.VITE_APP_BASE_API + '/'+import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE+'/schedulingPlan/worksubtask/importSuppleExcel',

            //种植作物
            raiseCropsCdOptions: [],
            //种植作物
            raiseCropsCdOptionsPlot: [],
            // 作业环节
            linkCodeOptions: [],
            // 作业环节- 地块选择
            linkCodeOptionsPlot: [],
            //成产流程
            prodProcessOptions: [],
            // 年度字典
            yearNoOptions: [],
            amTypeAll: [], //总分类
            //农机分类
            machineList: [],

            //农机选择

            showMachine: false,
            //地块选择
            plotParams: {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            },
            checkboxGroup: '',
            showPlot: false,
            plotListFilter: [],
            isLock: false,

            //计划面积：xxx万亩（分配面积
            planAreaCount: '0',
            //作业面积：xxx万亩
            workAreaCount: '0',
            //完成率：xx% (作业面积除分配面积
            finalizePart: '0'
        }
    },
    created() {

        getDicts('year_cd').then(response => {
            this.yearNoOptions = response.data
        })
        // getDicts('raise_crops').then(response => {
        //     this.raiseCropsCdOptions = response.data
        // })
        getDicts('prod_process').then(response => {
            this.prodProcessOptions = response.data
        })
        queryDictTree('3').then((res) => {
            this.machineList = res.data
        })
        this.lockFlag()
        this.getList()
        this.getRaise()
    },
    watch: {
        'plotParams.linkCode': function(val, old) {
            //this.plotParams.plotName = '';
            this.selectPlot()
        },
        /**监控年份变化**/
        'queryParams.year': function(val, oldVal) {
            if (val != null && val !== '' && !!this.queryParams.year) {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()

            }
        },
        'queryParams.orgCode': function(val, oldVal) {
            if (val != null && val !== '') {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.raiseCropsCd': function(val, oldVal) {
            this.queryParams.linkCode = null
            if (val != null && val !== '' && this.queryParams.prodProcessCode != null && this.queryParams.prodProcessCode !== '') {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.queryParams.prodProcessCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    console.log(response)
                    this.linkCodeOptions = response.data
                })
            } else {
                this.linkCodeOptions = []

            }
        },
        'queryParams.prodProcessCode': function(val, oldVal) {
            this.queryParams.linkCode = null
            if (val != null && val !== '' && this.queryParams.raiseCropsCd != null && this.queryParams.raiseCropsCd !== '') {
                //查机具品目
                cropProcess({
                    raiseCrops: this.queryParams.raiseCropsCd,
                    prodProcessCode: val,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                    console.log(response)
                })
            } else {
                this.linkCodeOptions = []
            }
        },
        'plotParams.raiseCropsCd': function(val, oldVal) {
            this.plotParams.linkCode = null
            if (val != null && val !== '' && this.plotParams.prodProcessCode != null && this.plotParams.prodProcessCode !== '') {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.plotParams.prodProcessCode,
                    orgCode: this.form.orgCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    console.log(response)
                    this.linkCodeOptionsPlot = response.data
                })
            } else {
                this.linkCodeOptionsPlot = []

            }
        },
        'plotParams.prodProcessCode': function(val, oldVal) {
            this.plotParams.linkCode = null
            if (val != null && val !== '' && this.plotParams.raiseCropsCd != null && this.plotParams.raiseCropsCd !== '') {
                //查机具品目
                cropProcess({
                    raiseCrops: this.plotParams.raiseCropsCd,
                    prodProcessCode: val,
                    orgCode: this.form.orgCode,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptionsPlot = response.data
                    console.log(response)
                })
            } else {
                this.linkCodeOptionsPlot = []
            }
        }
    },
    methods: {
      orgDictsUrl,
         amTypeAllChange(value){
            if(!value){
                this.amTypeAll = []
            }
        },
        formatFarmLeaderList(farmLeaderLists){
            if(farmLeaderLists&&farmLeaderLists.length>0){
                let arr = []
                farmLeaderLists.forEach(item=>{
                    arr.push(item.familyFarmLeaderName +" "+ item.familyFarmLeaderPhone)
                })
                return arr.join(",")
            }else{
                return ""
            }
        },
        getFamilyFarmLeader(){
            familyFarmLeader({plotName:this.form.plotNo}).then(res=>{
                this.familyFarmLeaderList = res.data
            })
        },
        getLicenseNoRules,
        getRaise() {
            //作物
            this.raiseCropsCdOptions = []
            this.queryParams.raiseCropsCd = ''
            this.queryParams.raiseCropsNm = ''
            getRaiseCrops({
                orgCode: this.queryParams.orgCode,
                year: this.queryParams.year
            }).then(response => {
                this.raiseCropsCdOptions = response.data
            })

        },
        //搜索地块
        handleCheckedChange(e) {
            console.log(this.plotName, e)
            if (!this.plotParams.plotName) {
                this.plotListFilter = this.plotList
            } else {
                this.plotListFilter = this.plotList.filter(item => {
                    return item.plotName.includes(this.plotParams.plotName)
                })
            }
        },
        lockFlag() {
            getLockFlag({
                orgCode: this.queryParams.orgCode,
                statYear: this.queryParams.year
            }).then(res => {
                this.isLock = res.data
            })
        },
        //地块列表-选择地块
        selectPlot() {
            let _this = this
            this.$refs['plotForm'].validate(valid => {
                if (valid) {
                    queryResPlotInfo(_this.plotParams).then(res => {
                        _this.plotList = res.data
                        _this.plotListFilter = res.data
                        _this.plotName = ''
                        _this.checkboxGroup = ''
                    })
                }
            })
        },
        resetQueryPlot() {
            this.plotParams = {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            }
            this.selectPlot()
        },
        //选择地块
        openSelecteFormPlot() {

            if (!this.form.orgCode) {
                this.$message({
                    message: '请选择归属单位',
                    type: 'warning'
                })
                return
            }

            if (!this.form.year) {
                this.$message({
                    message: '请选择作业年份',
                    type: 'warning'
                })
                return
            }
            this.showPlot = true
            this.plotParams.orgCode = this.form.orgCode
            this.plotParams.statYear = this.form.year

            //作物
            this.raiseCropsCdOptionsPlot = []
            this.plotParams.raiseCropsCd = ''
            getRaiseCrops({
                orgCode: this.plotParams.orgCode,
                year: this.plotParams.statYear
            }).then(response => {
                this.raiseCropsCdOptionsPlot = response.data
            })

            // this.selectPlot();
        },
        closePlot() {

            this.plotParams = {
                orgCode: '',
                statYear: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: ''
            }
            this.showPlot = false
            this.checkboxGroup = ''
            this.plotListFilter = []
            this.resetForm('plotForm')
        },
        //确定选择地块
        handleApply() {
            console.log(this.checkboxGroup)
            if (!this.checkboxGroup) {
                this.$modal.msgError('请选择地块')
                return
            }
            let name = this.plotListFilter.filter(item => {
                return item.plotNo == this.checkboxGroup
            })


            this.form.machs.forEach(item=>{
                item.familyFarmLeaderList = []
                // item.workUnitCost = ''
            })
            this.$refs['plotForm'].validate(valid => {
                if (valid) {
                    this.form.plotName = `${name[0].plotName}${name[0].pastPlotName ? `(${name[0].pastPlotName})` : ''}`
                    this.form.plotNo = name[0].plotNo
                    this.getFamilyFarmLeader()
                    this.form.landType = name[0].landType
                    this.form.plotArea = name[0].contrArea
                    this.form.raiseCropsCd = this.plotParams.raiseCropsCd
                    this.form.prodProcessCode = this.plotParams.prodProcessCode
                    this.form.linkCode = this.plotParams.linkCode
                    this.form.linkName = selectDictLabelForLink(this.linkCodeOptionsPlot, this.form.linkCode)
                    this.form.prodProcessName = selectDictLabel(this.prodProcessOptions, this.form.prodProcessCode)
                    this.form.raiseCropsNm = selectDictLabelForRaiseCrops(this.raiseCropsCdOptionsPlot, this.form.raiseCropsCd)
                    this.closePlot()
                }
            })
        },

        //农机弹窗
        openSelecteForm(index) {
            this.showMachine = true
            this.machsIndex = index
        },
        //添加农机
        addMachine() {
            //this.form.machs.push(this.machs);
            this.form.machs.push(
                {
                    driver: '',
                    drivers: [],//驾驶员列表，提交时记得删除
                    licenseNo: '',
                    machineId: '',
                    amOwnerId: '',
                    amOwnerName: '',
                    ownerCertNo: '',
                    inTime: '',
                    outTime: '',
                    amTypeCode1: '',
                    amTypeName1: '',
                    amTypeCode2: '',
                    amTypeName2: '',
                    amTypeCode3: '',
                    amTypeName3: '',
                    amGradeId: '',
                    amGradeName: '',
                    amModelId: '',
                    amModelName: '',
                    driverId: '',
                    amOwnerPhone: '',
                    driverName: '',
                    allocatedArea: '',
                    passArea: '',
                    workArea: '',
                    workUnitCost:'',
                    familyFarmLeaderList:[]
                }
            )
        },
        popMachine(index) {
            this.form.machs.splice(index, 1)
            //  this.form.machs.push(this.machs)
        },
        //选择农机-回调
        selectForm(e) {
            this.form.machs[this.machsIndex].powerMachine = e.powerMachine
            this.form.machs[this.machsIndex].licenseNo = e.licenseNo
            this.form.machs[this.machsIndex].machineId = e.machineId
            this.form.machs[this.machsIndex].amOwnerId = e.amOwnerId
            this.form.machs[this.machsIndex].amOwnerName = e.amOwnerName
            this.form.machs[this.machsIndex].ownerCertNo = e.ownerCertNo
            this.form.machs[this.machsIndex].amTypeCode1 = e.amTypeCode1
            this.form.machs[this.machsIndex].amTypeName1 = e.amTypeName1
            this.form.machs[this.machsIndex].amTypeCode2 = e.amTypeCode2
            this.form.machs[this.machsIndex].amTypeName2 = e.amTypeName2
            this.form.machs[this.machsIndex].amTypeCode3 = e.amTypeCode3
            this.form.machs[this.machsIndex].amTypeName3 = e.amTypeName3
            this.form.machs[this.machsIndex].amTypeName = e.amTypeName || ( (e.amTypeName1 || "") + (e.amTypeName2 || "") + (e.amTypeName3 || "") + (e.amGradeName || "") )
            this.form.machs[this.machsIndex].amGradeId = e.amGradeId
            this.form.machs[this.machsIndex].amGradeName = e.amGradeName
            this.form.machs[this.machsIndex].amModelId = e.amModelId
            this.form.machs[this.machsIndex].amModelName = e.amModelName
            this.form.machs[this.machsIndex].driverId = ''//选择赋值
            this.form.machs[this.machsIndex].driverName = ''//选择赋值
            this.form.machs[this.machsIndex].drivers = []
            this.form.machs[this.machsIndex].driver = null
            this.form.machs[this.machsIndex].workUnitCost = ''
            this.form.machs[this.machsIndex].familyFarmLeaderList = []




            let _this = this
            if (!e.machineId) {
                _this.$message({
                    message: '请选择农机',
                    type: 'error'
                })
                return false
            }

            //查询驾驶员
            queryDriverByMachId({ machineId: e.machineId }).then(res => {

                console.log(_this.form.machs, 11111)
                _this.form.machs[_this.machsIndex].drivers = res.data
                this.showMachine = false
                this.machsIndex = null
            })
        },
        //选择驾驶员
        driversChange(e, index) {
            let d = this.form.machs[index].drivers.filter(item => {
                return item.driverId == e
            })
            console.log('zlb', d[0])

            this.form.machs[index].driverId = d[0].driverId
            this.form.machs[index].driverName = d[0].driverName + ' ' + (d[0].phoneNo ? d[0].phoneNo : '')
            console.log(12345, e, index)
        },
        /** 查询作业记录（非统管）列表 */
        getList() {
            // this.loading = true
            listWorksubtask(this.queryParams).then(response => {
                this.worksubtaskList = response.data.records
                this.total = response.data.total
                // this.loading = false
                this.getSpanArr()
            })

            tipsWorksubtask(this.queryParams).then(res => {
                this.planAreaCount = res.data.planAreaCount
                this.workAreaCount = res.data.workAreaCount
                this.finalizePart = res.data.finalizePart
            })
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            //表格合并行
            if (
                columnIndex === 0 ||
                columnIndex === 1 ||
                columnIndex === 2 ||
                columnIndex === 3 ||
                columnIndex === 4 ||
                columnIndex === 5 ||
                columnIndex === 17
            ) {
                const _row = this.spanArr[rowIndex]
                const _col = _row > 0 ? 1 : 0
                return {
                    rowspan: _row,
                    colspan: _col
                }
            }
        },
        getSpanArr() {
            this.spanArr = []
            let data = this.worksubtaskList
            for (let i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1)
                    this.pos = 0
                } else {
                    // 判断当前元素与上一个元素是否相同
                    if (data[i].workTaskNo === data[i - 1].workTaskNo) {
                        this.spanArr[this.pos] += 1
                        this.spanArr.push(0)
                    } else {
                        this.spanArr.push(1)
                        this.pos = i
                    }
                }
            }
        },
        // 取消按钮
        cancel() {
            this.open = false
            this.reset()
        },
        // 表单重置
        reset() {
            this.form = {
                year: '',
                orgCode: '',
                plotNo: '',
                plotName: '',
                landType: '',
                plotArea: '',
                raiseCropsCd: '',
                prodProcessCode: '',
                linkCode: '',
                raiseCropsNm: '',
                prodProcessName: '',
                linkName: '',
                machs: [{
                    driver: '',
                    drivers: [],//驾驶员列表，提交时记得删除
                    licenseNo: '',
                    machineId: '',
                    amOwnerId: '',
                    amOwnerName: '',
                    ownerCertNo: '',
                    inTime: '',
                    outTime: '',
                    amTypeCode1: '',
                    amTypeName1: '',
                    amTypeCode2: '',
                    amTypeName2: '',
                    amTypeCode3: '',
                    amTypeName3: '',
                    amGradeId: '',
                    amGradeName: '',
                    amModelId: '',
                    amModelName: '',
                    driverId: '',
                    amOwnerPhone: '',
                    driverName: '',
                    allocatedArea: '',
                    passArea: '',
                    workArea: '',
                    workUnitCost:'',
                    familyFarmLeaderList:[]

                }]
            }
            this.resetForm('form')
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.amTypeCode1 = this.amTypeAll[0] || ''
            this.queryParams.amTypeCode2 = this.amTypeAll[1] || ''
            this.queryParams.amTypeCode3 = this.amTypeAll[2] || ''
            this.queryParams.amGradeId = this.amTypeAll[3] || ''
            this.queryParams.page = 1
            this.queryParams.page = 1
            this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm')
            this.amTypeAll = []
            this.handleQuery()
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset()
            this.form.year = String(new Date().getFullYear())
            this.form.orgCode = useUserStore().currentOrgCode
            this.open = true
            this.familyFarmLeaderList = []
            this.title = '添加作业记录（统管）'
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset()
            const workTaskNo = row.workTaskNo || this.ids
            let _this = this

            this.$refs['form'].validate(valid => {
                if (valid) {
                    getWorksubtask(workTaskNo).then(response => {
                        _this.form = response.data
                        _this.form.workTime = _this.parseTime(response.data.workTime, '{y}-{m}-{d}')
                        _this.open = true
                        _this.title = '修改作业记录（统管）'
                        cropProcess({
                            raiseCrops: _this.form.raiseCropsCd,
                            prodProcessCode: _this.form.prodProcessCode,
                            statYear: _this.form.year
                        }).then(response => {
                            _this.linkCodeOptions = response.data
                        })
                    })
                }
            })
        },
        /** 详情按钮操作 */
        handleShow(row) {
            this.reset()
            const workTaskNo = row.workTaskNo || this.ids
            let _this = this
            getWorksubtask(workTaskNo).then(response => {
                _this.form = response.data
                // _this.getFamilyFarmLeader()
                _this.form.workTime = _this.parseTime(response.data.workTime, '{y}-{m}-{d}')
                _this.openInfo = true
                cropProcess({
                    raiseCrops: _this.form.raiseCropsCd,
                    prodProcessCode: _this.form.prodProcessCode,
                    statYear: _this.form.year
                }).then(response => {
                    _this.linkCodeOptions = response.data
                })
            })
        },
        raiseCropsCdChange(val, clear) {

            if (clear == true) {
                this.form.linkCode = null
                if (val != null && val !== '' && this.form.prodProcessCode != null && this.form.prodProcessCode !== '') {
                    //环节字典
                    cropProcess({
                        raiseCrops: val,
                        prodProcessCode: this.form.prodProcessCode,
                        statYear: this.form.year
                    }).then(response => {
                        this.linkCodeOptions = response.data
                    })
                } else {
                    this.linkCodeOptions = []
                }
            }
        },
        prodProcessCodeChange(val, clear) {
            if (clear == true) {
                this.form.linkCode = null
                if (val != null && val !== '' && this.form.raiseCropsCd != null && this.form.raiseCropsCd !== '') {
                    //查询作业环节
                    cropProcess({
                        raiseCrops: this.form.raiseCropsCd,
                        prodProcessCode: val,
                        statYear: this.form.year
                    }).then(response => {
                        this.linkCodeOptions = response.data
                    })
                } else {
                    this.linkCodeOptions = []
                }
            }
        },
        /** 提交按钮 */
        submitForm() {

            let _this = this
            this.$refs['form'].validate(valid => {
                if (valid) {

                    let timeValidate = _this.form.machs.every(function(item) {
                        let workBeginTime = !!item.inTime ? new Date(item.inTime).getTime() : ''
                        let workEndTime = !!item.outTime ? new Date(item.outTime).getTime() : ''
                        return workBeginTime && workEndTime && workBeginTime <= workEndTime
                    })
                    if (!timeValidate) {
                        _this.$message({
                            message: '\'作业结束时间\'需要晚于\'作业开始时间\'',
                            type: 'error'
                        })
                        return false
                    }
                    let timeValidate2 = _this.form.machs.every(function(item) {
                        let workBeginTime = !!item.inTime ? new Date(item.inTime).getTime() : ''
                        let workEndTime = !!item.outTime ? new Date(item.outTime).getTime() : ''
                        let yearBegin = new Date(workBeginTime).getFullYear()//开始时间年份等于当前选择的年份
                        let yearEnd = new Date(workEndTime).getFullYear()
                        return yearBegin == _this.form.year && yearEnd == _this.form.year
                    })
                    if (!timeValidate2) {
                        _this.$message({
                            message: '\'作业开始/结束时间\'，必须为当前作业年份',
                            type: 'error'
                        })
                        return false
                    }
                    this.form.machs.forEach((m, i) => {
                        if (m.familyFarmLeaderList && m.familyFarmLeaderList.length > 0) {
                            let arr = []
                            m.familyFarmLeaderList.forEach(it => {
                                arr.push({
                                    familyFarmLeaderIdNumber: it.familyFarmLeaderIdNumber,
                                    familyFarmLeaderName: it.familyFarmLeaderName,
                                    familyFarmLeaderPhone: it.familyFarmLeaderPhone
                                })
                            })
                            m.familyFarmLeaderList = arr
                        }
                        delete m.drivers
                        delete m.driver
                    })
                    if (this.form.workTaskNo != null) {
                        updateWorksubtask(this.form).then(response => {
                           this.$modal.msgSuccess('修改成功')
                            this.open = false
                            this.getList()
                        })
                    } else {
                        addWorksubtask(this.form).then(response => {
                           this.$modal.msgSuccess('新增成功')
                            this.open = false
                            this.getList()
                        })
                    }
                }
            })
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const workTaskNos = row.workTaskNo || this.ids
            this.$confirm('是否确认删除作业记录（统管）为"' + row.year + '年' + row.plotNameAndArea + '的' + row.raiseCropsNm + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(function() {
                return Array.isArray(workTaskNos) ? delWorksubtasks(workTaskNos) : delWorksubtask(workTaskNos)
            }).then(() => {
                this.getList()
               this.$modal.msgSuccess('删除成功')
            })
        },
        exportTemplate() {
            window.location.href = exportTemplate()
        },
        //组织机构下拉
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
            this.lockFlag()
        },
        //组织机构下拉
        handleOrgChange2({orgCode}) {
            this.form.orgCode = orgCode
            this.clearPlot()
        },
        clearPlot() {
            this.form.plotNo = null
            this.form.plotName = null
        },
        changeVal(e) {
            e == null ? this.form.workTime = '' : null
        }
    }
}


</script>
<style scoped lang="scss">
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';
.right {
    float: right;
    font-size: 14px;
}

.colorblue {
    color: blue;
}

:deep(.upload-file-uploader) {
    margin-bottom: 0;
}

.select-btn {
    height: 35px;
    position: absolute;
    right: 11px;
    bottom: 13px
}

.dialog-tab {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-weight: bold;
    height: 40px;
    background: #e6f0fc;
    line-height: 40px;
    margin: 20px 0;
    padding: 0 15px;
}

:deep(.form-style .el-form-item){
    margin-right: 50px;
    width: 100%;

    .el-date-editor {
        width: 100% !important;
    }
}

:deep(.is-center.el-dialog){
    margin-top: 30vh !important;
}

:deep(.form-style .el-form-item .el-form-item__label) {
    text-align: left;
}

:deep(.form-style .el-form-item) {
    display: flex;
    flex-direction: column;
    text-align: left;
}
:deep(.el-tag.el-tag--info){
    background-color: transparent;
    border:none;
    color: #606266;
    padding-left: 0;
    font-size: 14px;
    padding-right: 0;
    .el-tag__close{
        display: none;
    }
}
:deep(.el-dialog__body .el-tag--info+.el-tag--info){
   margin-left: 0;
   &::before{
     content: ',';
   }
}
.plot-dia {
    .el-radio {
        display: inline-block;
    }
}

</style>
