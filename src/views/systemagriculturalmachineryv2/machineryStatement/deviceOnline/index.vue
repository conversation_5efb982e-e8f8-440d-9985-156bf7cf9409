<template>
  <div class="mainbg" style="overflow-y: auto">
    <iframe :style="`width:100%;height:${height}px;`" :src="`${iframeUrl}${params}`" frameborder="0" width="800"></iframe>
  </div>

</template>

<script>

import useVehiclesStore from "@/store/modules/vehicles"
import { mapWritableState } from 'pinia'
export default {
  name: 'deviceOnline',
  computed:{
    ...mapWritableState(useVehiclesStore, ['area']),
  },
  data() {
    return {
      //org_code是权限参数;tree是显示的内容
      params: '',
      iframeUrl:'http://**********:8080/webroot/decision/view/report?viewlet=04_农机管理系统/01_报表/02_设备在线统计报表.cpt&',
      height: document.body.clientHeight - 50

    }
  },
  created() {
    const treesCode = this.area.map(item => {return item.orgCode});
    this.params = `tree=`+ treesCode[0] +`&org_code=`+treesCode.join(',');
    console.log('>>>>>>created:-params: ', this.params)

  },
}
</script>

<style lang="scss" scoped>

</style>
