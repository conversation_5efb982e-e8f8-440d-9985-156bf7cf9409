<template>
    <div class="app-container">
        <el-form ref="queryForm" :model="queryParams" label-width="100px" class="form-line" v-show="showSearch"
            :disabled="currentLevel === 3">
            <el-row :gutter="15">
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" style="width: 100%;" :defaultOrgCode="queryParams.orgCode" :placeholderText="'请选择归属'"
                            :disabled="disabled1" @handleOrgCode="handleOrgChange"></OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                        <el-select v-model="queryParams.year" placeholder="请选择年份" clearable :disabled="disabled1">
                            <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="牌照号" prop="licenseNo">
                        <el-input v-model="queryParams.licenseNo" placeholder="请填写牌照号" clearable maxlength="30"
                            :disabled="disabled1">
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="机主姓名" prop="amOwnerName">
                        <el-input v-model="queryParams.amOwnerName" placeholder="请填写机主姓名" clearable maxlength="30"
                            :disabled="disabled1">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="15">
                <el-col :span="6">
                    <el-form-item label="机主手机号码" prop="contactNum">
                        <el-input v-model="queryParams.contactNum" placeholder="请输入机主手机号码" clearable maxlength="11"
                            :disabled="disabled1" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select v-model="queryParams.raiseCropsCd" clearable placeholder="请选择种植作物"
                            :disabled="disabled2">
                            <el-option v-for="dict in raiseCropsCdOptions" :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="生产流程" prop="prodProcessCode">
                        <el-select v-model="queryParams.prodProcessCode" clearable placeholder="请选择生产流程"
                            :disabled="disabled2">
                            <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select v-model="queryParams.linkCode" clearable placeholder="请选择作业环节" :disabled="disabled2">
                            <el-option v-for="dict in linkCodeOptions" :key="dict.linkCode" :label="dict.linkName"
                                :value="dict.linkCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="text-right" align="right">
                <el-button icon="Refresh"  @click="resetQuery">重置
                </el-button>
                <el-button icon="Search"  type="primary" @click="handleQuery" v-hasPermi="[
                    'machineOperatorReport:machine:page',
                    'machineOperatorReport:link:page',
                    'machineOperatorReport:driver:page',
                ]">搜索
                </el-button>
            </div>
        </el-form>
        <el-row :gutter="10" class="mb8 ">
            <el-col :span="1.5">
                <!--导出excel-->
                <!-- // TODO: 导出权限 -->
                <exceldownload ref="exceldownload" :exceltext="'导出'" icon="Download" :param="queryParams"
                    :url="exportUrl" v-hasPermi="[
                        'machineOperatorReport:machine:export',
                        'machineOperatorReport:link:export',
                        'machineOperatorReport:driver:export',
                    ]"></exceldownload>
            </el-col>
            <el-col :span="1.5">
                <el-button icon="Back"  @click="toBack" v-if="currentLevel > 1">返回上一级
                </el-button>
            </el-col>
            <right-toolbar  v-model:showSearch="showSearch"
                @queryTable="getList"></right-toolbar>
        </el-row>
        <div class="con-work-msg" v-if="currentLevel !== 1">
            作业总面积：{{ totalArea }}亩，合格面积：{{
                approvedArea
            }}亩，合格率：{{ passPercent }}
        </div>
        <el-table ref="tableRef" :data="tableData" :height="showSearch
            ? `calc(100vh - 340px - ${currentLevel > 1 ? 20 : 0}px)`
            : `calc(100vh - 220px - ${currentLevel > 1 ? 20 : 0}px)`
            " :key="tableKey">
            <template v-for="item in columns">
                <el-table-column v-bind="item" :key="item.key" v-if="item.visible">
                    <template #default="{row,column,$index}">
                        <div v-if="item.key === 'plotName'">
                            {{ row.plotName }}<br />
                            （{{ row.plotArea }}亩）
                        </div>
                        <div v-else>
                            {{
                                item.formatter
                                    ? item.formatter(
                                        row,
                                        column,
                                        row[item.key],
                                        $index
                                    )
                                    : row[item.key]
                            }}
                        </div>
                    </template>
                </el-table-column>
            </template>
            <el-table-column align="center" fixed="right" label="操作" width="110" v-if="currentLevel !== 3">
                <template #default="{row}">
                    <!-- // TODO: 查询明细权限 -->
                    <el-button  link type="primary" icon="Search" @click="handleDetail(row)">查询明细
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination ref="paginationRef" v-show="total > 0" v-model:limit="queryParams.rows"
            :page-sizes="[10, 20, 50, 100]" v-model:page="queryParams.page" :total="total"
            @pagination="onPaginationChange" />
    </div>
</template>
<script>
import exceldownload from '@/views/systemagriculturalmachineryv2/components/exceldownload'
import { cropProcess } from '@/api/systemagriculturalmachineryv2/dispatch/operational'
import { selectDictLabelForRaiseCrops } from "@/utils/cop";
import {
    machineQueryList,
    linkQueryList,
    linkQueryTips,
    driverQueryList,
    driverQueryTips,
} from '@/api/systemagriculturalmachineryv2/machineryStatement/jobAccount'

// 导出URL
const exportUrlObj = {
    1: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/report/machineOperatorReport/machine/exportExcel`,
    2: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/report/machineOperatorReport/link/exportExcel`,
    3: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/report/machineOperatorReport/driver/exportExcel`,
}
import useUserStore from "@/store/modules/user"
import { getDicts, getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import {orgDictsUrl} from "@/api/systemagriculturalmachineryv2/orgDict.js";
export default {
    name: '/report/machineOperatorReport',
    components: {
        exceldownload,
    },
    data() {
        return {
            // 当前列表级别, 默认为1级
            currentLevel: 1,
            // 存储对应列表级别的分页信息，以备返回时恢复
            pageOfLevel: {
                1: {
                    page: 1,
                    rows: 10,
                },
                2: {
                    page: 1,
                    rows: 10,
                },
                3: {
                    page: 1,
                    rows: 10,
                },
            },
            // 显示搜索条件
            showSearch: true,
            // 年份选项
            yearNoOptions: [],
            // 种植作物选项
            raiseCropsCdOptions: [],
            // 生产流程选项
            prodProcessOptions: [],
            // 作业环节选项
            linkCodeOptions: [],
            // 作业总面积
            totalArea: '',
            // 合格面积
            approvedArea: '',
            // 合格率
            passPercent: '',
            tableKey: `table${Math.random()}`,
            LEVEL_1_COLUMNS: [
                {
                    label: '归属',
                    disabled: false,
                    checked: true,
                    key: 'orgName',
                    prop: 'orgName',
                    align: 'center',
                    minWidth: 300,
                    visible: true,
                },
                {
                    label: '年份',
                    disabled: false,
                    checked: true,
                    key: 'year',
                    prop: 'year',
                    align: 'center',
                    minWidth: 90,
                    visible: true,
                },
                {
                    label: '牌照号',
                    disabled: false,
                    checked: true,
                    key: 'licenseNo',
                    prop: 'licenseNo',
                    align: 'center',
                    minWidth: 180,
                    visible: true,
                },
                {
                    label: '机主',
                    disabled: false,
                    checked: true,
                    key: 'amOwnerName',
                    prop: 'amOwnerName',
                    align: 'center',
                    minWidth: 220,
                    visible: true,
                },
                {
                    label: '作业总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'workArea',
                    prop: 'workArea',
                    align: 'center',
                    minWidth: 110,
                    visible: true,
                },
                {
                    label: '合格总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'passArea',
                    prop: 'passArea',
                    align: 'center',
                    minWidth: 110,
                    visible: true,
                },
                {
                    label: '作业时长(小时)',
                    disabled: false,
                    checked: true,
                    key: 'workTotalTime',
                    prop: 'workTotalTime',
                    align: 'center',
                    minWidth: 120,
                    visible: true,
                },
                {
                    label: '作业合格率',
                    disabled: false,
                    checked: true,
                    key: 'passRate',
                    prop: 'passRate',
                    align: 'center',
                    minWidth: 100,
                    visible: true,
                },
            ],
            LEVEL_2_COLUMNS: [
                {
                    label: '归属',
                    disabled: false,
                    checked: true,
                    key: 'orgName',
                    prop: 'orgName',
                    align: 'center',
                    minWidth: 200,
                    visible: true,
                },
                {
                    label: '年份',
                    disabled: false,
                    checked: true,
                    key: 'year',
                    prop: 'year',
                    align: 'center',
                    width: 90,
                    visible: true,
                },
                {
                    label: '牌照号',
                    disabled: false,
                    checked: true,
                    key: 'licenseNo',
                    prop: 'licenseNo',
                    align: 'center',
                    width: 120,
                    visible: true,
                },
                {
                    label: '机主',
                    disabled: false,
                    checked: true,
                    key: 'amOwnerName',
                    prop: 'amOwnerName',
                    align: 'center',
                    width: 170,
                    visible: true,
                },
                {
                    label: '种植作物',
                    disabled: false,
                    checked: true,
                    key: 'raiseCropsCd',
                    prop: 'raiseCropsCd',
                    formatter: this.dataFormat,
                    align: 'center',
                    width: 90,
                    visible: true,
                },
                {
                    label: '生产流程',
                    disabled: false,
                    checked: true,
                    key: 'prodProcessName',
                    prop: 'prodProcessName',
                    align: 'center',
                    minWidth: 150,
                    visible: true,
                },
                {
                    label: '作业环节',
                    disabled: false,
                    checked: true,
                    key: 'linkName',
                    prop: 'linkName',
                    align: 'center',
                    minWidth: 150,
                    visible: true,
                },
                {
                    label: '作业总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'workArea',
                    prop: 'workArea',
                    align: 'center',
                    width: 110,
                    visible: true,
                },
                {
                    label: '合格总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'passArea',
                    prop: 'passArea',
                    align: 'center',
                    width: 110,
                    visible: true,
                },
                {
                    label: '作业时长(小时)',
                    disabled: false,
                    checked: true,
                    key: 'workTotalTime',
                    prop: 'workTotalTime',
                    align: 'center',
                    width: 120,
                    visible: true,
                },
                {
                    label: '作业合格率',
                    disabled: false,
                    checked: true,
                    key: 'passRate',
                    prop: 'passRate',
                    align: 'center',
                    width: 100,
                    visible: true,
                },
            ],
            LEVEL_3_COLUMNS: [
                {
                    label: '归属',
                    disabled: false,
                    checked: true,
                    key: 'orgName',
                    prop: 'orgName',
                    align: 'center',
                    minWidth: 200,
                    visible: true,
                },
                {
                    label: '年份',
                    disabled: false,
                    checked: true,
                    key: 'year',
                    prop: 'year',
                    align: 'center',
                    width: 90,
                    visible: true,
                },
                {
                    label: '牌照号',
                    disabled: false,
                    checked: true,
                    key: 'licenseNo',
                    prop: 'licenseNo',
                    align: 'center',
                    width: 120,
                    visible: true,
                },
                {
                    label: '机主',
                    disabled: false,
                    checked: true,
                    key: 'amOwnerName',
                    prop: 'amOwnerName',
                    align: 'center',
                    width: 170,
                    visible: true,
                },
                {
                    label: '种植作物',
                    disabled: false,
                    checked: true,
                    key: 'raiseCropsCd',
                    prop: 'raiseCropsCd',
                    formatter: this.dataFormat,
                    align: 'center',
                    width: 90,
                    visible: true,
                },
                {
                    label: '生产流程',
                    disabled: false,
                    checked: true,
                    key: 'prodProcessName',
                    prop: 'prodProcessName',
                    align: 'center',
                    minWidth: 150,
                    visible: true,
                },
                {
                    label: '作业环节',
                    disabled: false,
                    checked: true,
                    key: 'linkName',
                    prop: 'linkName',
                    align: 'center',
                    minWidth: 150,
                    visible: true,
                },
                {
                    label: '驾驶员',
                    disabled: false,
                    checked: true,
                    key: 'driverName',
                    prop: 'driverName',
                    align: 'center',
                    width: 170,
                    visible: true,
                },
                {
                    label: '地块名称',
                    disabled: false,
                    checked: true,
                    key: 'plotName',
                    prop: 'plotName',
                    align: 'center',
                    minWidth: 120,
                    visible: true,
                },
                {
                    label: '作业总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'workArea',
                    prop: 'workArea',
                    align: 'center',
                    width: 110,
                    visible: true,
                },
                {
                    label: '合格总面积(亩)',
                    disabled: true,
                    checked: true,
                    key: 'passArea',
                    prop: 'passArea',
                    align: 'center',
                    width: 110,
                    visible: true,
                },
                {
                    label: '作业时长(小时)',
                    disabled: false,
                    checked: true,
                    key: 'workTotalTime',
                    prop: 'workTotalTime',
                    align: 'center',
                    width: 120,
                    visible: true,
                },
                {
                    label: '作业合格率',
                    disabled: false,
                    checked: true,
                    key: 'passRate',
                    prop: 'passRate',
                    align: 'center',
                    width: 100,
                    visible: true,
                },
            ],
            tableData: [],
            total: 10,
            // 查询参数
            queryParams: {
                page: 1,
                rows: 10,
                orgCode: useUserStore().currentOrgCode,
                year: String(new Date().getFullYear()),
                // 牌照号
                licenseNo: null,
                // 机主姓名
                amOwnerName: null,
                // 机主手机号
                contactNum: null,
                // 种植作物
                raiseCropsCd: null,
                // 生产流程
                prodProcessCode: null,
                // 作业环节
                linkCode: null,
                // 农机id
                machineId: null,
            },
        }
    },
    computed: {
        disabled1() {
            return this.currentLevel !== 1
        },
        disabled2() {
            return this.currentLevel !== 2
        },
        columns() {
            const columnObj = {
                1: this.LEVEL_1_COLUMNS,
                2: this.LEVEL_2_COLUMNS,
                3: this.LEVEL_3_COLUMNS,
            }
            return columnObj[this.currentLevel]
        },
        exportUrl() {
            return exportUrlObj[this.currentLevel]
        }
    },
    watch: {
        'queryParams.year': function (val) {
            if (val) {
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.orgCode': function (val) {
            if (val || val === 0) {
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.raiseCropsCd': function (val) {
            this.queryParams.linkCode = ''
            if (
                (val || val === 0) &&
                (this.queryParams.prodProcessCode ||
                    this.queryParams.prodProcessCode === 0)
            ) {
                this.getLinkOptions()
            } else {
                this.linkCodeOptions = []
            }
        },
        'queryParams.prodProcessCode': function (val) {
            this.queryParams.linkCode = ''
            if (
                (val || val === 0) &&
                (this.queryParams.raiseCropsCd ||
                    this.queryParams.raiseCropsCd === 0)
            ) {
                this.getLinkOptions()
            } else {
                this.linkCodeOptions = []
            }
        },
    },
    created() {
        this.getYearOptions()
        this.getProdProcessOptions()
        this.getRaise()
        this.getList()
    },
    methods: {
      orgDictsUrl,
        //组织机构下拉
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
        },
        async getYearOptions() {
            try {
                const { data } = await getDicts('year_cd')
                this.yearNoOptions = data
            } catch (error) { }
        },
        async getProdProcessOptions() {
            try {
                const { data } = await getDicts('prod_process')
                this.prodProcessOptions = data
            } catch (error) { }
        },
        async getRaise() {
            try {
                this.raiseCropsCdOptions = []
                this.queryParams.raiseCropsCd = ''
                const { orgCode, year } = this.queryParams
                const { data } = await getRaiseCrops({
                    orgCode,
                    year,
                })
                this.raiseCropsCdOptions = data
            } catch (error) { }
        },
        async getLinkOptions() {
            try {
                const {
                    year: statYear,
                    raiseCropsCd: raiseCrops,
                    prodProcessCode,
                } = this.queryParams
                const { data } = await cropProcess({
                    statYear,
                    raiseCrops,
                    prodProcessCode,
                })
                this.linkCodeOptions = data
            } catch (error) { }
        },
        resetQuery() {
            if (this.currentLevel === 3) return;
            let queryObj = {}
            switch (this.currentLevel) {
                case 1:
                    queryObj = {
                        year: String(new Date().getFullYear()),
                        orgCode: useUserStore().currentOrgCode,
                        licenseNo: null,
                        amOwnerName: null,
                        contactNum: null,
                        raiseCropsCd: null,
                        prodProcessCode: null,
                        linkCode: null,
                        page: 1,
                        rows: 10,
                        machineId: null,
                    }
                    break;
                case 2:
                    const { orgCode, year, licenseNo, amOwnerName, contactNum, machineId } = this.queryParams
                    queryObj = {
                        orgCode,
                        year,
                        licenseNo,
                        amOwnerName,
                        contactNum,
                        machineId,
                        raiseCropsCd: null,
                        prodProcessCode: null,
                        linkCode: null,
                    }
            }
            this.queryParams = queryObj
            this.getList()
        },
        handleQuery() {
            this.queryParams.page = 1
            this.getList()
        },
        onPaginationChange() {
            this.getList()
        },
        async getList(params = this.queryParams) {
            try {
                this.tableKey = `table${Math.random()}`
                let requestListFn
                switch (this.currentLevel) {
                    case 1:
                        requestListFn = machineQueryList
                        break
                    case 2:
                        requestListFn = linkQueryList
                        break
                    case 3:
                        requestListFn = driverQueryList
                        break
                }
                const { data } = await requestListFn(params)
                this.tableData = data.records
                this.total = data.total
                this.getTips()
                console.log(`>>>>>>>>>>>>第${this.currentLevel}层数据：`, data)
            } catch (error) {
                console.log('>>>>>>>err: ', error)
            }
        },
        // 获取列表统计信息
        async getTips() {
            try {
                let requestTipsFn
                switch (this.currentLevel) {
                    case 1:
                        requestTipsFn = null
                        break
                    case 2:
                        requestTipsFn = linkQueryTips
                        break
                    case 3:
                        requestTipsFn = driverQueryTips
                        break
                }
                if (requestTipsFn) {
                    const { orgCode, year, machineId, raiseCropsCd, prodProcessCode, linkCode } = this.queryParams
                    const { data: { passArea, passRate, workArea } } = await requestTipsFn({
                        orgCode,
                        year,
                        machineId,
                        raiseCropsCd,
                        prodProcessCode,
                        linkCode
                    })
                    this.totalArea = workArea
                    this.approvedArea = passArea
                    this.passPercent = passRate
                    console.log('>>>>>>列表统计信息: ', data)
                }
            } catch (error) { }
        },
        dataFormat(row, column) {
            return selectDictLabelForRaiseCrops(
                this.raiseCropsCdOptions,
                row[column.property] ? row[column.property].toString() : ''
            )
        },
        handleDetail(row) {
            // 记录离开时的分页信息
            const { page, rows } = this.queryParams
            const { machineId, raiseCropsCd, prodProcessCode, linkCode } = row
            this.pageOfLevel[this.currentLevel] = {
                page,
                rows,
            }
            this.currentLevel++
            this.queryParams.machineId = machineId
            this.queryParams.page = 1
            this.queryParams.rows = 10
            this.queryParams.raiseCropsCd = raiseCropsCd
            this.queryParams.prodProcessCode = prodProcessCode
            this.$nextTick(() => {
                this.queryParams.linkCode = linkCode
                this.getList()
            })
        },
        async toBack() {
            // 恢复离开时的分页信息
            this.currentLevel--
            const { page, rows } = this.pageOfLevel[this.currentLevel]
            this.queryParams.raiseCropsCd = null
            this.queryParams.prodProcessCode = null
            this.queryParams.linkCode = null
            if (this.currentLevel === 1) {
                this.queryParams.machineId = null
            }
            await this.getList({
                ...this.queryParams,
                page,
                rows
            })
            // 解决返回上级列表时，页码回显不正确问题
            this.$nextTick(() => {
                this.queryParams.page = page
                this.queryParams.rows = rows
            })
        },
    },
}
</script>
<style scoped lang="scss">
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';

.con-work-msg {
    font-size: 14px;
    color: #606266;
    text-align: right;
    margin-bottom: 5px;
}
</style>
