<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" v-show="showSearch"
      label-width="68px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="任务名称" prop="distTaskName">
            <el-input v-model="queryParams.distTaskName" placeholder="请输入任务名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="统计年份" prop="statYear">
            <el-date-picker clearable v-model="queryParams.statYear" type="year" value-format="YYYY"
              placeholder="选择统计年份" format="YYYY" style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="任务开始时间" label-width="100px" prop="taskBeginTime">
            <el-date-picker clearable v-model="queryParams.taskBeginTime" type="date" style="width: 100%;"
              value-format="YYYYMMDD" format="YYYY-MM-DD" :disabled-date="pickerOptionsStatQuery.disabledDate"
              placeholder="请选择任务开始时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="任务结束时间" label-width="100px" prop="taskEndTime">
            <el-date-picker clearable v-model="queryParams.taskEndTime" type="date" style="width: 100%;" value-format="YYYYMMdd"
              format="YYYY-MM-DD" :disabled-date="pickerOptionsEndQuery.disabledDate" placeholder="请选择任务结束时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="是否完成" prop="isComplete">
            <el-select v-model="queryParams.isComplete" placeholder="请选择是否完成" @clear="queryParams.dataStatus = null"
              :clearable="false">
              <el-option v-for="item in pubIfOptions" :key="item.code" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18" align="right">
            <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
        </el-col>
      </el-row>

      <!-- <el-form-item label="报表的file_id" prop="fileId">
        <el-input
          v-model="queryParams.fileId"
          placeholder="请输入报表的file_id"
          clearable

          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报表的sheet_id" prop="sheetId">
        <el-input
          v-model="queryParams.sheetId"
          placeholder="请输入报表的sheet_id"
          clearable

          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="填报开始时间" prop="taskBeginTime">
        <el-input
          v-model="queryParams.taskBeginTime"
          placeholder="请输入填报开始时间"
          clearable

          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="填报截止时间" prop="taskEndTime">
        <el-input
          v-model="queryParams.taskEndTime"
          placeholder="请输入填报截止时间"
          clearable

          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生效状态 1：生效，2：失效" prop="statusCd">
        <el-input
          v-model="queryParams.statusCd"
          placeholder="请输入生效状态 1：生效，2：失效"
          clearable

          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"

          @click="handleAdd"

        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"

          :disabled="single"
          @click="handleUpdate"

        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"

          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :height="tableConfig.height" :data="rptDistributeTaskList"
      @selection-change="handleSelectionChange">
      <el-table-column label="序号" type="index" align="center" width="80">
      </el-table-column>
      <!-- <el-table-column label="任务下发id" align="center" prop="distTaskId" /> -->
      <el-table-column label="任务名称" align="center" prop="distTaskName">
         <template #default="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.distTaskName" placement="top">
            <el-button  link type="primary" @click="handleDetail(scope.row)">{{ scope.row.distTaskName }}</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="填报截止时间" align="center" prop="taskEndTime" />
      <el-table-column label="实际填报时间" align="center" prop="statDate" />
      <el-table-column label="数据状态" align="center" prop="dataStatus">
         <template #default="scope">
          <span v-if="scope.row.orgCode.length > 2">{{ dataStatusFormat(scope.row) }}</span>
          <span v-if="scope.row.orgCode.length < 10 && scope.row.orgCode.length!= 6 "
            :style="{ color: scope.row.childCommitCount == scope.row.childCount ? '#70B604' : '#D9001B' }">({{ scope.row.childCommitCount }}/{{ scope.row.childCount }})</span>
        </template>
      </el-table-column>
      <el-table-column label="是否日报" align="center" prop="isDaily">
         <template #default="scope">
          {{ scope.row.isDaily == '1' ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="填报规定" align="center" prop="isDaily">
         <template #default="scope">
          {{ scope.row.isDaily == '1' ? '需要当日填报' : '可提前1日填报' }}
          <div v-if="scope.row.isDaily == '1'"
            :style="{ color: handleDate(scope.row.todayDateStr) >= handleDate(scope.row.taskEndTime) ? '#70B604' : '#D9001B' }">
            {{ handleDate(scope.row.todayDateStr) >= handleDate(scope.row.taskEndTime) ? '现可以填报' : '暂不可填报' }}
          </div>
          <div v-if="scope.row.isDaily == '0'"
            :style="{ color: (scope.row.todayDateStr == scope.row.taskEndTime || handleDate(scope.row.todayDateStr) + 1 >= handleDate(scope.row.taskEndTime)) ? '#70B604' : '#D9001B' }">
            {{ (scope.row.todayDateStr == scope.row.taskEndTime || handleDate(scope.row.todayDateStr) + 1 >=
              handleDate(scope.row.taskEndTime)) ? '现可以填报' :'暂不可填报'}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="290" class-name="small-padding fixed-width">
         <template #default="scope">
          <div class="rptDistributeTask">
            <el-button  link type="primary" @click="handleDetail(scope.row)" icon="View">浏览</el-button>
            <el-button v-show="scope.row.isDaily == '1'" style="float: right;"  link type="primary"
              @click="handleConfirm(scope.row)" :disabled="isComplete"
              v-hasPermi="['rptDataInfo:endTask']" icon="TurnOff">提前结束日报</el-button>
            <el-button v-show="scope.row.isDaily == '0'" style="float: right;width: 74px;text-align: left;"
              link type="primary" @click="handleDelete(scope.row)" :disabled="isComplete"
              v-hasPermi="['rptDataInfo:logicDeleteById']" icon="Delete">删除</el-button>
            <el-switch v-hasPermi="['rptDataInfo:updateIsCanModifyByDataId']"
              style="float: right;margin-top: 3px;margin-right: 10px;" v-model="scope.row.isCanModify"
              active-color="#409EFF" active-text="限制数据" inactive-text="基础数据" :disabled="isComplete"
              @change="handelSwitch(scope.row)" active-value="1" inactive-value="0">
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="报表的file_id" align="center" prop="fileId" /> -->
      <!-- <el-table-column label="报表的sheet_id" align="center" prop="sheetId" /> -->
      <!-- <el-table-column label="填报开始时间" align="center" prop="taskBeginTime" /> -->
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <!-- <el-table-column label="生效状态 1：生效，2：失效" align="center" prop="statusCd" /> -->
    </el-table>

    <pagination v-show="total > 0" :total="total"  v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />

    <!-- 添加或修改任务记录对话框 -->
    <el-dialog :title="title"  v-model="open"width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务名称" prop="distTaskName">
          <el-input v-model="form.distTaskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="报表的file_id" prop="fileId">
          <el-input v-model="form.fileId" placeholder="请输入报表的file_id" />
        </el-form-item>
        <el-form-item label="报表的sheet_id" prop="sheetId">
          <el-input v-model="form.sheetId" placeholder="请输入报表的sheet_id" />
        </el-form-item>
        <el-form-item label="填报开始时间" prop="taskBeginTime">
          <el-input v-model="form.taskBeginTime" placeholder="请输入填报开始时间" />
        </el-form-item>
        <el-form-item label="填报截止时间" prop="taskEndTime">
          <el-input v-model="form.taskEndTime" placeholder="请输入填报截止时间" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="生效状态 1：生效，2：失效" prop="statusCd">
          <el-input v-model="form.statusCd" placeholder="请输入生效状态 1：生效，2：失效" />
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listRptDistributeTask, getRptDistributeTask, delRptDistributeTask, addRptDistributeTask, updateRptDistributeTask, delRptDistributeTasks, updateIsCanModifyByDistTaskId } from "@/api/systemagriculturalmachineryv2/fillIn/rptDistributeTask";
import { endTask } from '@/api/systemagriculturalmachineryv2/groupReport/fill';
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
import { selectDictLabel } from '@/utils/cop'
export default {
  name: "/rptDistributeTaskVue",
  components: {
  },
  data() {
    return {
      pickerOptionsStatQuery: {
        disabledDate: time => {
          if (this.queryParams.taskEndTime) {
            let str = this.queryParams.taskEndTime
            let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
            return (time.getTime() > new Date(timeStr).getTime())
          }
        }
      },
      pickerOptionsEndQuery: {
        disabledDate: time => {
          if (this.queryParams.taskBeginTime) {
            let str = this.queryParams.taskBeginTime
            let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
            return time.getTime() < new Date(timeStr).getTime() - (24 * 60 * 60 * 1000)
          }
        }
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务记录表格数据
      rptDistributeTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        rows: 10,
        distTaskName: null,
        fileId: null,
        sheetId: null,
        isDaily: null,
        taskBeginTime: null,
        taskEndTime: null,
        statusCd: null,
        isComplete: "0",
        statYear: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      tableConfig: {
        height: window.innerHeight - 350,
      },
      dataStatusOptions: [],
      pubIfOptions: [],
      isComplete: false,
    };
  },
  created() {
    let historyParams = localStorage.getItem('url')
    if (!!historyParams) {
      this.queryParams = JSON.parse(historyParams)
    } else {
      this.queryParams.statYear = new Date().getFullYear().toString()
    }
    this.queryParams.statYear = new Date().getFullYear().toString()
    let data_status = getDicts("data_status").then(response => {
      this.dataStatusOptions = response.data;
    });
    getDicts("pub_if").then(response => {
      this.pubIfOptions = response.data;
      if (!historyParams) {
        this.queryParams.isComplete = '0'
      }
    });
    Promise.all([data_status]).then(() => {
      this.getList()
      localStorage.removeItem('url');
    })
  },
  mounted() {
    let _this = this
    _this.$nextTick(() => {
      window.addEventListener('resize', () => {
        _this.tableKey++
      })
    })
    window.onresize = () => {
      return (() => {
        this.$nextTick(() => {
          this.tableConfig.height = document.querySelector('.app-wrapper').offsetHeight - document.querySelector('.fixed-header').offsetHeight - document.querySelector('.queryClass').offsetHeight - document.querySelector('.mb8').offsetHeight - 117;
        })
      })();
    };
    this.$nextTick(() => {
      this.tableConfig.height = document.querySelector('.app-wrapper').offsetHeight - document.querySelector('.fixed-header').offsetHeight - document.querySelector('.queryClass').offsetHeight - document.querySelector('.mb8').offsetHeight - 117;
    })
    // this.updateButtonStyle()
  },
  watch: {
    //搜索隐藏调整表格大小
    showSearch() {
      this.$nextTick(() => {
        this.tableConfig.height = document.querySelector('.app-wrapper').offsetHeight - document.querySelector('.fixed-header').offsetHeight - document.querySelector('.queryClass').offsetHeight - document.querySelector('.mb8').offsetHeight - 117;
      })
    },
    // 'form.statYear'(newValue,oldValue){

    // },
  },
  methods: {
    handleDate(str) {
      let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
      let date = new Date(timeStr).getTime() / 86400000
      return date
    },
    /** 查询任务记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.systemCode = 'systemagriculturalmachineryv2'
      listRptDistributeTask(this.queryParams).then(response => {
        this.rptDistributeTaskList = response.data.dataList.records;
        this.total = response.data.dataList.total;
        this.loading = false;
        if (this.queryParams.isComplete === '1') {
          this.isComplete = true
        } else {
          this.isComplete = false
        }
      });
    },
    dataStatusFormat(row, column) {
      return selectDictLabel(this.dataStatusOptions, row.dataStatus != null && row.dataStatus != undefined ? row.dataStatus.toString() : "");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        distTaskId: null,
        distTaskName: null,
        fileId: null,
        sheetId: null,
        isDaily: null,
        taskBeginTime: null,
        taskEndTime: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        statusCd: null
      };
      this.resetForm("form");
    },
    handleDetail(val) {
      localStorage.setItem('url', JSON.stringify(this.queryParams))
      this.$router.push({
        path: '/systemagriculturalmachineryv2/agriculturalInformationFill/agriculturalInformationFill',
        query: {
          fileName: val.fileName,
          orgCode: val.orgCode,
          fileType: val.fileType,
          belongMenu: val.belongMenu,
          fileAuthName: val.fileAuthName,
          fillType: val.fillType,
          distTaskName: val.distTaskName,
          taskBeginTime: val.taskBeginTime,
          taskEndTime: val.taskEndTime,
          isDaily: val.isDaily,
          todayDateStr: val.todayDateStr,
          distTaskId: val.distTaskId,
          sheetId: val.sheetId
        },
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.statYear = new Date().getFullYear().toString()
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.distTaskId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const distTaskId = row.distTaskId || this.ids
      getRptDistributeTask(distTaskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改任务记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.distTaskId != null) {
            updateRptDistributeTask(this.form).then(response => {
             this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRptDistributeTask(this.form).then(response => {
             this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const distTaskIds = row.distTaskId || this.ids;
      this.$confirm('是否确认删除该非日报?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return Array.isArray(distTaskIds) ? delRptDistributeTasks(distTaskIds) : delRptDistributeTask(distTaskIds);
      }).then(() => {
        this.getList();
       this.$modal.msgSuccess("删除成功");
      })
    },
    handleConfirm(row) {
      let _this = this
      this.$confirm('是否提前结束该日报?', '提前结束提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        endTask({ sheetId: row.sheetId, orgCode: '86', distTaskId: row.distTaskId }).then((response) => {
          _this.$message({
            type: 'success',
            message: '日报提前结束成功!'
          });
          _this.getList();
        })
      }).catch(() => {

      });
    },
    handelSwitch(item) {
      let params = {
        dataId: item.dataId,
        isCanModify: item.isCanModify
      }
      updateIsCanModifyByDistTaskId(params).then(res => {
        this.getList();
       this.$modal.msgSuccess("操作成功");
      })
    }

  }
};


</script>
<style scoped lang="scss">
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';
.rptDistributeTask .el-switch__label {
  font-weight: 400 !important;
  color: #dcdfe6;
}

.rptDistributeTask .el-switch__label.is-active {
  color: #409EFF !important;
}

.rptDistributeTask .el-switch__label * {
  font-size: 12px !important;
}

.rptDistributeTask .el-button.el-button--mini.is-disabled {
  color: #dcdfe6 !important;
}
</style>
