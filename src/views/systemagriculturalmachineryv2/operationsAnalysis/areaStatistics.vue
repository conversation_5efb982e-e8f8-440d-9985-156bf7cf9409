<template>
    <div class="app-container">
        <el-form ref="queryParams" :model="queryParams" v-show="showSearch" label-position="left" label-width="90px" inline>
            <!--设备号-->
            <sn v-model="queryParams.dsn" @clearDsn="clearDsn"/>

            <!--作业时间-->
            <el-form-item label="作业时间">
                <el-date-picker
                    v-model="dateRange"

                    style="width: 240px"
                    value-format="YYYY-MM-DD"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"

                    @click="checkNumber"

                >导出
                </el-button>
            </el-col>
            <el-col :span="1.5" class="title">
                <div>作业面积总计：{{ calcMu(areaCount) }}亩</div>
            </el-col>
            <el-col :span="1.5" class="title">
                <div class="margin-left-20">作业时长总计：{{ tCount ? tCount : 0 }}小时</div>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table :data="areaStatisticsList">
            <el-table-column label="用户名" prop="name" align="center" show-overflow-tooltip/>
            <el-table-column label="作业名" prop="workname" align="center" show-overflow-tooltip/>
            <el-table-column label="出厂编号" prop="dsn" align="center" show-overflow-tooltip/>
            <el-table-column label="设备厂家" prop="facCode" align="center" show-overflow-tooltip :formatter="formatterFacCode"/>
            <el-table-column label="设备型号" prop="dmd" align="center" show-overflow-tooltip/>
            <el-table-column label="铭牌号" prop="dnpn" align="center" show-overflow-tooltip/>
            <el-table-column label="田块类型" prop="field" align="center" show-overflow-tooltip :formatter="formatterField"/>
            <el-table-column label="作业类型" prop="wtype" align="center" show-overflow-tooltip :formatter="formatterWtype"/>
            <el-table-column label="作业面积(亩)" prop="area" align="center" :formatter="formatterArea" show-overflow-tooltip/>
            <el-table-column label="作业时间" prop="date" align="center" show-overflow-tooltip>
                 <template #default="scope">
                    {{ formatIOSyyyymmdd(scope.row.start, '-') }} - {{ formatIOSyyyymmdd(scope.row.end, '-') }}
                </template>
            </el-table-column>
            <el-table-column label="作业地址" prop="endAddress" align="center" show-overflow-tooltip/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120">
                 <template #default="scope">
                    <el-button

                        link type="primary"
                        icon="Search"
                        @click="handleDrawer(scope.row)"
                    >详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
             v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        />

        <precision-details v-if="drawerVisible" :size="drawerSize" :table-row-data="tableRowData" :visible="drawerVisible" @close="closeDrawer"/>
    </div>
</template>

<script>
import Sn from "@/views/systemagriculturalmachineryv2/components/FormItem/Sn";
import { mapWritableState, mapActions } from 'pinia'
import useVehiclesStore from "@/store/modules/vehicles"
import PrecisionDetails from "@/views/systemagriculturalmachineryv2/operationsAnalysis/components/PrecisionDetails";
import {fetchList} from "@/api/systemagriculturalmachineryv2/precisionOperation"
import {formatIOSyyyymmdd} from "@/views/systemagriculturalmachineryv2/script/Utils/DateAndTimeCommon"
import {calcMu} from "@/views/systemagriculturalmachineryv2/script/Utils/BaseCommon";
import {formatterArea, formatterFacCode, formatterField, formatterWtype} from "@/views/systemagriculturalmachineryv2/script/table_formatter"
import axios from 'axios'
import {FAC_TYPE_CODE} from '@/views/systemagriculturalmachineryv2/script/ConstantDefinition'

/**
 * 组件说明: 精确作业
 */
export default {
    name: 'areaStatistics',
    components: {
        Sn, PrecisionDetails
    },
    data() {
        return {
            FAC_TYPE_CODE,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 测试类表格数据
            areaStatisticsList: [],
            // 日期范围
            dateRange: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            areaCount: '', // 作业面积总计
            tCount: 0, // 作业时长总计
            // 查询参数
            queryParams: {
                page: 1,
                rows: 10,
                dsn: null,
                name: null,
                field: null,
                wtype: null,
                crop: null,
                dtype: null,
                ttype: null,
                uuid: null,
                type: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            // 查看详情 drawer
            drawerVisible: false,
            tableRowData: {},
            drawerSize: '50%',
            flag: true
        };
    },
    created() {
        const uuid = localStorage.getItem('precisionUuid')
        localStorage.removeItem('precisionUuid')
        if (uuid) {
            this.queryParams.uuid = uuid
            this.sn.map((item) => {
                if (this.queryParams.uuid == item.uuid) {
                    this.queryParams.dsn = item.dsn
                }
            })
            const dsn = localStorage.getItem('precisionDsn')
            localStorage.removeItem('precisionDsn')
            if (dsn) {
                this.queryParams.dsn = dsn
                this.sn.map((item) => {
                    if (this.queryParams.dsn == item.dsn) {
                        this.queryParams.uuid = item.uuid
                    }
                })
            }
        }
        this.getList();
        this.flag = false
    },
    computed: {
        ...mapWritableState(useVehiclesStore, ['area','sn']),
    },
    watch: {
        area(val) {
            if (val.length > 0) {
                this.getList()
            }
        }
    },
    methods: {
        formatIOSyyyymmdd,
        formatterArea,
        formatterField,
        formatterWtype,
        formatterFacCode,
        calcMu,
        fromData() {
            if (this.queryParams.dsn) {
                this.sn.map((item) => {
                    if (this.queryParams.dsn == item.dsn) {
                        this.queryParams.uuid = item.uuid
                    }
                })
            }
        },
        clearDsn() {
            this.queryParams.dsn = null
            this.queryParams.uuid = null
        },
        /** 查询表集合 */
        getList() {
            if (!this.flag) {
                this.fromData()
            }
            const param = {
                page: {
                    currentPage: this.queryParams.page, // 当前页
                    pageCount: this.queryParams.rows, // 每页显示条数
                },

                params: {
                    uuid: this.queryParams.uuid,
                    name: this.queryParams.name,
                    field: this.queryParams.field,
                    wtype: this.queryParams.wtype,
                    crop: this.queryParams.crop,
                    dtype: this.queryParams.dtype,
                    ttype: this.queryParams.ttype,
                    startTime: this.dateRange != '' ? this.dateRange[0] + ' 00:00:00' : '', // 安装时间 - 开始时间
                    endTime: this.dateRange != '' ? this.dateRange[1] + ' 23:59:59' : '', // 安装时间 - 结束时间
                }
            }
            fetchList(param).then(response => {
                this.areaStatisticsList = response.data.works && response.data.works.datas
                this.total = response.data.works ? response.data.works.page.total : 0
                this.areaCount = response.data.workCount && response.data.workCount.areaCount
                this.tCount = response.data.workCount && response.data.workCount.tcount
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.queryParams.dsn = null
            this.queryParams.uuid = null
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 查看详情
        handleDrawer(row) {
            this.tableRowData = Object.assign({}, row)
            const width = document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width
            if (width > 1200) {
                this.drawerSize = '30%'
            } else {
                this.drawerSize = '50%'
            }
            this.drawerVisible = true
        },
        // 关闭详情
        closeDrawer() {
            this.drawerVisible = false
        },
        /** 导出按钮操作 */
        handleDerive() {
            if (this.areaStatisticsList.length < 1) {
                this.$message({
                    message: '暂无数据!',
                    type: 'info'
                });
                return
            } else {
                axios.post(
                    window.VITE_APP_BASE_API + '/'+import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE+'/work/list/excel',
                    {
                        uuid: this.queryParams.uuid,
                        name: this.queryParams.name,
                        field: this.queryParams.field,
                        wtype: this.queryParams.wtype,
                        crop: this.queryParams.crop,
                        dtype: this.queryParams.dtype,
                        ttype: this.queryParams.ttype,
                        startTime: this.dateRange != '' ? this.dateRange[0] + ' 00:00:00' : '', // 安装时间 - 开始时间
                        endTime: this.dateRange != '' ? this.dateRange[1] + ' 23:59:59' : '', // 安装时间 - 结束时间
                        excelExportFrom: 'areaStatistics',
                    },
                    {
                        responseType: 'blob',
                    }
                ).then(function (response) {
                    var blob = new Blob([response.data])
                    var downloadElement = document.createElement('a');
                    var href = window.URL.createObjectURL(blob); //创建下载的链接
                    downloadElement.href = href;
                    downloadElement.download = '面积统计.xls'; //下载后文件名
                    document.body.appendChild(downloadElement);
                    downloadElement.click(); //点击下载
                    document.body.removeChild(downloadElement); //下载完成移除元素
                    window.URL.revokeObjectURL(href); //释放掉blob对象
                })
                    .catch(function (error) {
                        console.log(error);
                    });
            }
        },
        checkNumber() {
            if (this.areaStatisticsList.length < 1) {
                this.$message({
                    message: '暂无数据!',
                    type: 'info'
                });
                return
            }
            this.submitting = true
            const param = {
                page: {
                    currentPage: this.queryParams.page, // 当前页
                    pageCount: this.queryParams.rows, // 每页显示条数
                },
                params: {
                    dsn: this.queryParams.dsn,
                    name: this.queryParams.name,
                    tel: this.queryParams.tel,
                    dnpn: this.queryParams.dnpn,
                    dfac: this.queryParams.facCode,
                    dtype: this.queryParams.dtype,
                    dmd: this.queryParams.dmd,
                    startTime: this.dateRange != '' ? this.dateRange[0] + ' 00:00:00' : '', // 安装时间 - 开始时间
                    endTime: this.dateRange != '' ? this.dateRange[1] : ' 23:59:59', // 安装时间 - 结束时间
                }
            }
            this.handleDerive()
        },
    }
}
</script>

<style scoped>
.title {
    font-size: 14px;
    color: #606266;
    line-height: 28px;
    font-weight: bold
}
</style>
