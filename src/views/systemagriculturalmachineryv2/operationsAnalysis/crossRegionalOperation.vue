<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="68px" class="form-line">
            <el-row :gutter="30">
                <el-col :span="6">
                    <sn style="width: 100%;" v-model="queryParams.dsn" class="sn-component" />
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业时间">
                        <el-date-picker v-model="dateRange" style="width: 240px" value-format="YYYY-MM-DD"
                            type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6" :offset="6" class="text-right" align="right">
                    <el-button icon="Refresh"  @click="resetQuery">重置
                    </el-button>
                    <el-button type="primary" icon="Search"  @click="handleQuery">搜索
                    </el-button>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button plain icon="Download"  @click="handleDerive">导出
                </el-button>
            </el-col>
            <el-col :span="1.5" class="title">
                <div>作业面积总计：{{ calcMu(areaCount) }}亩</div>
            </el-col>
            <el-col :span="1.5" class="title">
                <div class="margin-left-20">作业时长总计：{{ tCount }}小时</div>
            </el-col>
            <!--      <el-col :span="1.5" class="title">-->
            <!--        记录条数：{{ total }}-->
            <!--      </el-col>-->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table :data="operationList" :height="tableHeight">
            <el-table-column label="出厂编号" prop="dsn" align="center" show-overflow-tooltip />
            <el-table-column label="用户名" prop="name" align="center" show-overflow-tooltip />
            <el-table-column label="作业名" prop="workname" align="center" show-overflow-tooltip />
            <el-table-column label="设备厂家" prop="facCode" align="center" show-overflow-tooltip
                :formatter="formatterFacCode" />
            <el-table-column label="设备型号" prop="dmd" align="center" show-overflow-tooltip />
            <el-table-column label="铭牌号" prop="dnpn" align="center" show-overflow-tooltip />
            <el-table-column label="作业面积(亩)" prop="area" align="center" :formatter="formatterArea"
                show-overflow-tooltip />
            <el-table-column label="作业时间" prop="date" align="center" show-overflow-tooltip>
                <template #default="scope">
                    {{ formatIOSyyyymmdd(scope.row.start, "-") }} -
                    {{ formatIOSyyyymmdd(scope.row.end, "-") }}
                </template>
            </el-table-column>
            <el-table-column label="安装区域" prop="belongArea" align="center" show-overflow-tooltip />
            <el-table-column label="作业地址" prop="endAddress" align="center" show-overflow-tooltip />
        </el-table>

        <pagination :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
            @pagination="getList" />
        <!-- <pagination
            v-show="total > 0"
            :total="total"
             v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        /> -->
    </div>
</template>

<script>
/**
 * 组件说明: 跨区域作业
 */
import Sn from "@/views/systemagriculturalmachineryv2/components/FormItem/Sn";
import { fetchList } from "@/api/systemagriculturalmachineryv2/crossRegionalOperation";
import { formatIOSyyyymmdd } from "@/views/systemagriculturalmachineryv2/script/Utils/DateAndTimeCommon";
import { formatterArea, formatterFacCode } from "@/views/systemagriculturalmachineryv2/script/table_formatter";
import axios from "axios";
import useVehiclesStore from "@/store/modules/vehicles"
import { mapWritableState } from 'pinia'
import { calcMu } from "@/views/systemagriculturalmachineryv2/script/Utils/BaseCommon";
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
export default {
    name: "CrossRegionalOperation",
    components: {
        Sn,
    },
    mixins: [calcTabelHeight],
    data() {
        return {
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 测试类表格数据
            operationList: [],
            // 日期范围
            dateRange: "",
            // 弹出层标题
            title: "",
            areaCount: "", // 作业面积总计
            tCount: 0, // 作业时长总计
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                page: 1,
                rows: 10,
                dsn: null,
                crop: null,
                dtype: null,
                field: null,
                name: null,
                ttype: null,
                uuid: null,
                startTime: null,
                endTime: null,
                orgCode: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
        };
    },
    computed: {
        ...mapWritableState(useVehiclesStore, ['area','areaOriginal']),
    },
    created() {
        this.getList();
    },
    methods: {
        calcMu,
        formatIOSyyyymmdd,
        formatterArea,
        formatterFacCode,
        /** 查询测试类列表 */
        getList() {
            this.queryParams.uuid = null;
            // 20230928：原逻辑用dsn换出uuid来
            // 权限越高得用户map越大，使用起来越卡
            // 暂时替换dsn作为检索条件
            // if (this.queryParams.dsn) {
            //     this.sn.map((item) => {
            //         if (this.queryParams.dsn === item.dsn) {
            //             this.queryParams.uuid = item.uuid;
            //         }
            //     });
            // }
            const param = {
                page: {
                    currentPage: this.queryParams.page, // 当前页
                    pageCount: this.queryParams.rows, // 每页显示条数
                },
                params: {
                    crop: this.queryParams.crop,
                    dtype: this.queryParams.dtype,
                    field: this.queryParams.field,
                    name: this.queryParams.name,
                    ttype: this.queryParams.ttype,
                    type: "1",
                    uuid: this.queryParams.uuid,
                    dsn: this.queryParams.dsn,
                    startTime: this.dateRange && this.dateRange != "" ? this.dateRange[0] : "", // 安装时间 - 开始时间
                    endTime: this.dateRange && this.dateRange != "" ? this.dateRange[1] : "", // 安装时间 - 结束时间
                    orgCode: this.queryParams.belongArea,
                },
            };
            fetchList(param).then((response) => {
                this.operationList =
                    response.data.works && response.data.works.datas;
                this.total = response.data.works
                    ? response.data.works.page.total
                    : 0;
                this.areaCount =
                    response.data.workCount &&
                    response.data.workCount.areaCount;
                this.tCount =
                    response.data.workCount && response.data.workCount.tcount;
                this.operationList.forEach((item) => {
                    this.formatterBelongArea(item);
                });
            });
        },
        formatterBelongArea(row) {
            const arr = this.areaOriginal;
            const temp = [];
            for (let i = 0; i < arr.length; i++) {
                if (row.orgCode === arr[i].orgCode) {
                    temp[0] = arr[i].orgFullName;
                }
            }
            row.belongArea = temp.filter((item) => item).join("-");
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.queryParams.dsn = null;
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /** 导出按钮操作 */
        handleDerive() {
            if (this.operationList.length < 1) {
                this.$message({
                    message: "暂无数据!",
                    type: "info",
                });
                return;
            } else {
                axios
                    .post(
                        window.VITE_APP_BASE_API + `/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/work/list/excel`,
                        {
                            crop: this.queryParams.crop,
                            dtype: this.queryParams.dtype,
                            field: this.queryParams.field,
                            name: this.queryParams.name,
                            ttype: this.queryParams.ttype,
                            type: "1",
                            uuid: this.queryParams.uuid,
                            dsn: this.queryParams.dsn,
                            startTime: this.dateRange && this.dateRange != "" ? this.dateRange[0] : "", // 安装时间 - 开始时间
                            endTime: this.dateRange && this.dateRange != "" ? this.dateRange[1] : "", // 安装时间 - 结束时间
                            orgCode: this.queryParams.belongArea,
                            excelExportFrom: 'crossRegionalOperation',
                        },
                        {
                            responseType: "blob",
                        }
                    )
                    .then(function (response) {
                        var blob = new Blob([response.data]);
                        var downloadElement = document.createElement("a");
                        var href = window.URL.createObjectURL(blob); //创建下载的链接
                        downloadElement.href = href;
                        downloadElement.download = "跨区域作业分析.xls"; //下载后文件名
                        document.body.appendChild(downloadElement);
                        downloadElement.click(); //点击下载
                        document.body.removeChild(downloadElement); //下载完成移除元素
                        window.URL.revokeObjectURL(href); //释放掉blob对象
                    })
                    .catch(function (error) {
                        console.log(error);
                    });
            }
        },
    },
};
</script>
<style scoped lang="scss">
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';

.title {
    font-size: 14px;
    color: #606266;
    line-height: 28px;
    font-weight: bold;
}
</style>
