<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="68px"
            class="con-search-form form-line">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                        <el-select style="width: 100%;" v-model="queryParams.year" placeholder="请选择年份">
                            <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" style="width: 100%;"
                            @handleOrgCode="handleOrgChange" :defaultOrgCode="queryParams.orgCode"
                            :placeholderText="'请选择归属'"></OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select style="width: 100%" v-model="queryParams.raiseCropsCd" clearable
                            placeholder="请选择种植作物">
                            <el-option v-for="dict in raiseCropsCdOptions" :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select style="width: 100%" v-model="queryParams.linkCode" clearable placeholder="请选择作业环节">
                            <el-option v-for="dict in linkCodeOptions" :key="dict.linkCode" :label="dict.linkName"
                                :value="dict.linkCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="农机分类" prop="amTypeAll">
                        <el-cascader  style="width: 100%" ref="orgSelect" v-model="amTypeAll" :options="machineList" @change="amTypeAllChange" placeholder="请选择农机分类"
                            :props="{
                                label: 'amTypeName',
                                value: 'amTypeCode',
                                expandTrigger: 'hover',
                                multiple: false,
                                checkStrictly: true,
                            }" :show-all-levels="false" collapse-tags clearable></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="牌照号" prop="licenseNo">
                        <el-input v-model="queryParams.licenseNo" clearable placeholder="请输入牌照号" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="机主姓名" prop="amOwnerName">
                        <el-input v-model="queryParams.amOwnerName" placeholder="请输入机主姓名" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="地块名称" prop="plotName">
                        <el-input v-model="queryParams.plotName" clearable placeholder="请输入地块名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业状态" prop="workStatus">
                        <el-select style="width: 100%;" v-model="queryParams.workStatus" clearable filterable
                            placeholder="请选择作业状态">
                            <el-option v-for="dict in workStatusOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="出厂编号" prop="dsn">
                        <el-input v-model="queryParams.dsn" placeholder="请输入出厂编号" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="设备类型" prop="dtype">
                        <el-select style="width: 100%;" v-model="queryParams.dtype" placeholder="请选择设备类型" clearable>
                            <el-option v-for="dict in machineCodeList" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="设备厂商" prop="deviceFac">
                        <el-input v-model="queryParams.deviceFac" placeholder="请输入设备厂商" clearable />
                        <!-- <el-select style="width: 100%;" v-model="queryParams.deviceFac" placeholder="请选择设备厂商" clearable>
                            <el-option v-for="dict in FAC_TYPE_CODE" :key="dict.dictId" :label="dict.dictName"
                                :value="dict.dictId" />
                        </el-select> -->
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="农机编码" prop="machineCode">
                        <el-input v-model="queryParams.machineCode" placeholder="请输入农机编码" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="作业时间">
                        <el-date-picker v-model="dateRange" style="width: 100%" value-format="YYYY-MM-DD"
                            type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="more ? 12 : 6" class="text-right" align="right">
                    <el-button :icon="more ? 'ArrowUpBold' : 'ArrowDownBold'" @click="moreHander">更多
                    </el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置
                    </el-button>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <exceldownload ref="exceldownload" :exceltext="'导出'" v-show="false" icon="el-icon-download" :param="{ ...param }"
                    :url="url"></exceldownload>
                <el-button icon="Download" size="mini" @click="execDownload">导出</el-button>
            </el-col>
             <div style="display: flex;justify-content: flex-end;align-items: center;font-size: 14px;margin-left: auto;">
               <el-col :span="1.5" class="right" v-show="showworkArea">
                    计划面积：<span class="colorblue">{{ planArea }}</span>
                    ，作业面积：<span class="colorblue">{{ workArea }}</span>
                    ，完成率：<span class="colorblue">{{ completeRate }}</span>
                </el-col>
                 <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </div>
        </el-row>
        <el-table ref="tableRef" :data="worksubtaskList" border :height="tableHeight">
            <template v-for="item in columns">
                <el-table-column v-bind="item" :prop="item.key" :key="item.key" v-if="item.visible"
                    show-overflow-tooltip>
                    <template #default="scope">
                        <span>{{ item.formatter ? item.formatter(scope.row, scope.column, scope.row[item.key],
                            scope.$index) : scope.row[item.key] }}</span>
                    </template>
                </el-table-column>
            </template>
            <el-table-column label="操作" align="center" fixed="right" width="180">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleUpdate(scope.row)">详情
                    </el-button>
                    <el-button link type="primary" icon="DataAnalysis" @click="
                        handleJobAnalysis(scope.$index, scope.row, '查看')
                        ">分析
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
            @pagination="getList()" />
        <!-- 添加或修改作业记录（统管）对话框 -->
        <el-dialog :title="title" v-model="open" width="65%" append-to-body @closed="cancel"
            :close-on-click-modal="false">
            <el-form ref="form" :model="form" label-width="80px" label-position="top" disabled>
                <div style="font-size: 16px;">基本信息</div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="年份" prop="year">
                            <el-input v-model="form.year" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="归属" prop="orgName">
                            <el-input v-model="form.orgName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="牌照号" prop="licenseNo">
                            <el-input v-model="form.licenseNo" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="机主" prop="amOwnerName">
                            <el-input v-model="form.amOwnerName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" :title="form.amTypeFullName">
                        <el-form-item label="农机分类" prop="amTypeFullName">
                            <el-input v-model="form.amTypeFullName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="农机编码" prop="machineCode">
                            <el-input v-model="form.machineCode" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="出厂编号" prop="workMachCode">
                            <el-input v-model="form.workMachCode" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="设备类型" prop="deviceType">
                            <el-input v-model="form.deviceType" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="设备厂商" prop="companyName">
                            <el-input v-model="form.companyName" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <div style="font-size: 16px;">作业信息</div>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="地块名称" prop="plotNameDisplay">
                            <el-input v-model="form.plotNameDisplay" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="cropName">
                            <el-input v-model="form.cropName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业环节" prop="machineLinkName">
                            <el-input v-model="form.machineLinkName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="任务名称" prop="taskName">
                            <el-input v-model="form.taskName" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业状态" prop="taskStatus">
                            <el-input v-model="form.taskStatus" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业幅宽(m)" prop="workWide">
                            <el-input v-model="form.workWide" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业里程(m)" prop="workLen">
                            <el-input v-model="form.workLen" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业面积(亩)" prop="workArea">
                            <el-input v-model="form.workArea" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="合格面积(亩)" prop="workPassArea">
                            <el-input v-model="form.workPassArea" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="合格率" prop="qualifiedRate">
                            <el-input v-model="form.qualifiedRate" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="作业时间" prop="workTimeRange">
                            <el-input v-model="form.workTimeRange" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业时长(min)" prop="workDuration">
                            <el-input v-model="form.workDuration" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { orgDictsUrl } from "@/api/systemagriculturalmachineryv2/orgDict.js";
import { queryDictTree } from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'
import { cropProcess } from '@/api/systemagriculturalmachineryv2/dispatch/operational'
import { listAmcompanyAll } from "@/api/systemagriculturalmachineryv2/basicInfo/amcompany";
import FileUpload1 from "@/views/systemagriculturalmachineryv2/components/FileUpload1";
import exceldownload from "@/views/systemagriculturalmachineryv2/components/exceldownload";
import { format } from 'date-fns'
import { FAC_TYPE_CODE, TERMINAL_TYPE_CODE } from "@/views/systemagriculturalmachineryv2/script/ConstantDefinition";
import { listWorkTask_queryByPage, summary, queryMultDict } from "@/api/systemagriculturalmachineryv2/schedulingPlan/precisionOperationSuiHua";
import { getDicts, getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import useUserStore from "@/store/modules/user"
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
import { queryByCropProcess} from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'
export default {
    name: 'PrecisionOperation',
    components: {
        FileUpload1, exceldownload,
    },
    mixins: [calcTabelHeight],
    computed: {
        param() {
            this.queryParams.amTypeCode1 = this.amTypeAll[0] || "";
            this.queryParams.amTypeCode2 = this.amTypeAll[1] || "";
            this.queryParams.amTypeCode3 = this.amTypeAll[2] || "";
            this.queryParams.amGradeId = this.amTypeAll[3] || "";
            this.queryParams.jobStartTime = (this.dateRange && this.dateRange[0]) ? new Date(this.dateRange[0] + ' 00:00:00').getTime() : ''
            this.queryParams.jobEndTime = (this.dateRange && this.dateRange[1]) ? new Date(this.dateRange[1] + ' 23:59:59').getTime() : ''
            let par = {
                year: this.queryParams.year,
                orgCode: this.queryParams.orgCode,
                cropType: this.queryParams.raiseCropsCd,
                linkCode: this.queryParams.linkCode,
                amTypeCode1: this.queryParams.amTypeCode1,
                amTypeCode2: this.queryParams.amTypeCode2,
                amTypeCode3: this.queryParams.amTypeCode3,
                amGradeId: this.queryParams.amGradeId,
                licenseNo: this.queryParams.licenseNo,
                amOwnerName: this.queryParams.amOwnerName,
                plotName: this.queryParams.plotName,
                taskStatus: this.queryParams.workStatus,
                workMachCode: this.queryParams.dsn,
                workType: this.queryParams.dtype,
                deviceFac: this.queryParams.deviceFac,
                machineCode: this.queryParams.machineCode,
                workBeginTime: this.queryParams.jobStartTime,
                workEndTime: this.queryParams.jobEndTime,
            }
            return par
        },
    },
    data() {
        return {
            machineCodeList: [],
            completeRate: null,
            planArea: null,
            workArea: null,
            amTypeAll: [],
            TERMINAL_TYPE_CODE,
            FAC_TYPE_CODE,
            amcompanyList: [],
            dateRange: [],
            more: false,
            showConfig: false,
            columns: [
                {
                    label: '年份',
                    align: 'center',
                    key: 'year',
                    width: '80',
                    visible: true,
                },
                {
                    label: '归属',
                    align: 'center',
                    key: 'orgName',
                    'min-width': '120',
                    visible: true,
                },
                {
                    label: '牌照号',
                    align: 'center',
                    key: 'licenseNo',
                    width: '120',
                    visible: true,
                },
                {
                    label: '机主',
                    align: 'center',
                    key: 'amOwnerName',
                    width: '160',
                    visible: true,
                },
                {
                    label: '农机分类',
                    align: 'center',
                    key: 'amTypeFullName',
                    'min-width': '120',
                    visible: true,
                },
                {
                    label: '农机编码',
                    align: 'center',
                    key: 'machineCode',
                    width: '120',
                    visible: true,
                },
                {
                    label: '出厂编号',
                    align: 'center',
                    key: 'workMachCode',
                    width: '140',
                    visible: true,
                },
                {
                    label: '设备类型',
                    align: 'center',
                    key: 'deviceType',
                    width: '120',
                    visible: true,
                },

                {
                    label: '设备厂商',
                    align: 'center',
                    key: 'companyName',
                    width: '120',
                    visible: true,
                },
                {
                    label: '地块名称',
                    align: 'center',
                    key: 'plotNameDisplay',
                    width: '100',
                    visible: true,
                },
                {
                    label: '种植作物',
                    align: 'center',
                    key: 'cropName',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业环节',
                    align: 'center',
                    key: 'machineLinkName',
                    width: '160',
                    visible: true,
                },
                {
                    label: '任务名称',
                    align: 'center',
                    key: 'taskName',
                    'min-width': '200',
                    visible: true,
                },
                {
                    label: '作业状态',
                    align: 'center',
                    key: 'taskStatus',
                    width: '100',
                    visible: true,
                },
                {
                    label: '作业面积(亩)',
                    align: 'center',
                    key: 'workArea',
                    width: '100',
                    visible: true,
                },
                {
                    label: '合格面积(亩)',
                    align: 'center',
                    key: 'workPassArea',
                    width: '100',
                    visible: false,
                },
                {
                    label: '作业时间',
                    align: 'center',
                    key: 'workTimeRange',
                    width: '200',
                    visible: true,
                },
                {
                    label: '总作业时长',
                    align: 'center',
                    key: 'workDuration',
                    width: '100',
                    visible: true,
                },
                {
                    label: '合格率',
                    align: 'center',
                    key: 'qualifiedRate',
                    width: '100',
                    visible: false,
                },
            ],
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 作业补录（统管）表格数据
            worksubtaskList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                rows: 10,
                page: 1,
                year: String(new Date().getFullYear()),
                orgCode: useUserStore().currentOrgCode,
                raiseCropsCd: null,
                linkCode: null,
                licenseNo: null,
                amOwnerName: null,
                plotName: null,
                workStatus: null,
                dsn: null,
                dtype: null,
                deviceFac: null,
                machineCode: null,
            },
            // 表单参数
            form: {
                year: null,
                orgName: null,
                licenseNo: null,
                amOwnerName: null,
                amTypeName: null,
                machineCode: null,
                dsn: null,
                dtype: null,
                deviceFac: null,
                plotName: null,
                raiseCropsCd: null,
                linkCode: null,
                taskName: null,
                workStatus: null,
                jobWidth: null,
                len: null,
                workArea: null,
                passArea: null,
                passPercent: null,
                jobTotalTime: null,
            },
            // 表单校验
            rules: {

            },
            url: window.VITE_APP_BASE_API + `/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/precisionOperation/iotWorkTaskInfo/exportExcel`,
            //种植作物
            raiseCropsCdOptions: [],
            //种植作物
            raiseCropsCdOptionsPlot: [],
            // 作业环节
            linkCodeOptions: [],
            // 作业环节- 地块选择
            linkCodeOptionsPlot: [],
            //成产流程
            prodProcessOptions: [],
            // 年度字典
            yearNoOptions: [],
            amTypeAll: [], //总分类
            //农机分类
            machineList: [],
            //车辆类型
            machine_origin_type: [],
            workStatusOptions: [{code:0,name:'未开始'},{code:1,name:'进行中'},{code:2,name:'已完成'}],
            showworkArea: false
        }
    },
    created() {
        // getDicts("work_status").then((response) => {
        //     this.workStatusOptions = response.data;
        // });
        listAmcompanyAll({}).then(response => {
            this.amcompanyList = response.data;
        });
        getDicts('year_cd').then(response => {
            this.yearNoOptions = response.data
        })
        getDicts('prod_process').then(response => {
            this.prodProcessOptions = response.data
        })
        queryDictTree('1').then((res) => {
            this.machineList = res.data
        })
        getDicts("machine_origin_place").then(response => {
            this.machine_origin_place = response.data;
        });
        getDicts("machine_origin_type").then(response => {
            this.machine_origin_type = response.data;
        });
        queryMultDict({}).then(res => {
            this.machineCodeList = res.data
        })
        this.getList()
        this.getRaise()
    },
    watch: {
        /**监控年份变化**/
        'queryParams.year'(val, oldVal) {
            if (val != null && val !== '' && !!this.queryParams.year) {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.orgCode'(val, oldVal) {
            if (val != null && val !== '') {
                this.linkList = []
                this.queryParams.linkCode = ''
                this.getRaise()
            }
        },
        'queryParams.raiseCropsCd'(val, oldVal) {
            this.queryParams.linkCode = null
            if (val) {
                //环节字典
                queryByCropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.queryParams.prodProcessCode,
                    statYear: this.queryParams.year,
                    orgCode:this.queryParams.orgCode,
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            } else {
                this.linkCodeOptions = []
            }
        },
        'queryParams.prodProcessCode'(val, oldVal) {
            this.queryParams.linkCode = null
            if (val != null && val !== '' && this.queryParams.raiseCropsCd != null && this.queryParams.raiseCropsCd !== '') {
                //查机具品目
                cropProcess({
                    raiseCrops: this.queryParams.raiseCropsCd,
                    prodProcessCode: val,
                    statYear: this.queryParams.year
                }).then(response => {
                    this.linkCodeOptions = response.data
                })
            } else {
                this.linkCodeOptions = []
            }
        },
    },
    methods: {
        amTypeAllChange(value){
            if(!value){
                this.amTypeAll = []
            }
        },
        execDownload(){
            if (this.queryParams.jobStartTime) {
                let year = new Date(this.queryParams.jobStartTime).getFullYear()
                if (this.queryParams.year != year) {
                    this.$message.error(`不可选择超出选择年份的日期`)
                    return
                }
            }
            if (this.queryParams.jobEndTime) {
                let year = new Date(this.queryParams.jobEndTime).getFullYear()
                if (this.queryParams.year != year) {
                    this.$message.error(`不可选择超出选择年份的日期`)
                    return
                }
            }
            this.$refs.exceldownload.execDownload()
        },
        orgDictsUrl,
        handleJobAnalysis(index, row, title) {
            this.$router.push({
                path: '/systemagriculturalmachineryv2/operationsAnalysis/JobAnalysis',
                query: {
                    taskId: row.workTaskId,
                    type: row.workType,
                    plotName: row.plotName,
                    linkCode: row.linkCode
                },
            })
        },
        /** 格式化表格 */
        formatter(row, column, cellValue, index) {
            if (column.property == 'amStatus') {
                return this.selectDictLabel(this.amStatusOptions, cellValue);
            } else if (column.property == 'createTime') {
                return format(new Date(cellValue), 'yyyy-MM-dd HH:mm:ss');
            } else {
                return cellValue
            }
        },
        moreHander() {
            this.more = !this.more
        },
        getRaise() {
            //作物
            this.raiseCropsCdOptions = []
            this.queryParams.raiseCropsCd = ''
            this.queryParams.raiseCropsNm = ''
            getRaiseCrops({
                orgCode: this.queryParams.orgCode,
                year: this.queryParams.year
            }).then(response => {
                this.raiseCropsCdOptions = response.data
            })
        },
        /** 查询作业记录（非统管）列表 */
        getList() {
            this.loading = true
            this.queryParams.amTypeCode1 = this.amTypeAll[0] || "";
            this.queryParams.amTypeCode2 = this.amTypeAll[1] || "";
            this.queryParams.amTypeCode3 = this.amTypeAll[2] || "";
            this.queryParams.amGradeId = this.amTypeAll[3] || "";
            this.queryParams.jobStartTime = (this.dateRange && this.dateRange[0]) ? new Date(this.dateRange[0] + ' 00:00:00').getTime() : ''
            this.queryParams.jobEndTime = (this.dateRange && this.dateRange[1]) ? new Date(this.dateRange[1] + ' 23:59:59').getTime() : ''
            if (this.queryParams.jobStartTime) {
                let year = new Date(this.queryParams.jobStartTime).getFullYear()
                if (this.queryParams.year != year) {
                    this.$message.error(`不可选择超出选择年份的日期`)
                    return
                }
            }
            if (this.queryParams.jobEndTime) {
                let year = new Date(this.queryParams.jobEndTime).getFullYear()
                if (this.queryParams.year != year) {
                    this.$message.error(`不可选择超出选择年份的日期`)
                    return
                }
            }
            let par = {
                page: this.queryParams.page,
                rows: this.queryParams.rows,
                year: this.queryParams.year,
                orgCode: this.queryParams.orgCode,
                cropType: this.queryParams.raiseCropsCd,
                linkCode: this.queryParams.linkCode,
                amTypeCode1: this.queryParams.amTypeCode1,
                amTypeCode2: this.queryParams.amTypeCode2,
                amTypeCode3: this.queryParams.amTypeCode3,
                amGradeId: this.queryParams.amGradeId,
                licenseNo: this.queryParams.licenseNo,
                amOwnerName: this.queryParams.amOwnerName,
                plotName: this.queryParams.plotName,
                taskStatus: this.queryParams.workStatus,
                workMachCode: this.queryParams.dsn,
                workType: this.queryParams.dtype,
                deviceFac: this.queryParams.deviceFac,
                machineCode: this.queryParams.machineCode,
                workBeginTime: this.queryParams.jobStartTime,
                workEndTime: this.queryParams.jobEndTime,
            }
            listWorkTask_queryByPage(par).then(response => {
                this.worksubtaskList = response.data.records
                this.total = response.data.total
                this.loading = false
            })
            if (this.queryParams.linkCode) {
                summary(par).then(res => {
                    this.showworkArea = true
                    this.completeRate = res.data.completeRate
                    this.planArea = res.data.planArea
                    this.workArea = res.data.workArea
                })
            } else {
                this.showworkArea = false
            }
        },
        // 取消按钮
        cancel() {
            this.reset()
            this.open = false
        },
        // 表单重置
        reset() {
            this.form = {
                year: null,
                orgName: null,
                licenseNo: null,
                amOwnerName: null,
                amTypeName: null,
                machineCode: null,
                dsn: null,
                dtype: null,
                deviceFac: null,
                plotName: null,
                raiseCropsCd: null,
                linkCode: null,
                taskName: null,
                workStatus: null,
                jobWidth: null,
                len: null,
                workArea: null,
                passArea: null,
                passPercent: null,
                jobTotalTime: null,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1
            this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm')
            this.queryParams.year = String(new Date().getFullYear())
            this.queryParams.orgCode = useUserStore().currentOrgCode
            this.amTypeAll = []
            this.dateRange = []
            this.handleQuery()
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.title = '详情'
            listWorkTask_queryByPage({ workTaskId: row.workTaskId }).then(response => {
                console.log('response',response)
                let data = response.data.records[0] || {}
                this.form = data
                this.open = true
            })
        },
        //组织机构下拉
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
        },
    }
}

</script>
<style scoped lang="scss">
.right {
    font-size: 14px;
    padding-top: 4px;
}

.colorblue {
    color: blue;
}











</style>
