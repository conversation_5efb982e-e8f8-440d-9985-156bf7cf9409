<template>
  <el-drawer
    title="作业详情"
    v-model="drawer"
    :with-header="true"
    :size="size"
    style="font-weight: bold;"
    @closed="close"
  >
    <div class="verticalTable">
      <table border="0" cellpadding="0" cellspacing="1" style="margin-bottom: 20px">
        <tr>
          <td>用户名</td><td>{{ tableData.name }}</td>
        </tr>
        <tr>
          <td>出厂编号</td><td>{{ tableData.dsn }}</td>
        </tr>
        <tr>
          <td>设备厂家</td><td>{{ formatterFacCode({},{},tableData.facCode)  }}</td>
        </tr>
        <tr>
          <td>终端类型</td><td>{{ formatterD_type({},{},tableData.wdType)  }}</td>
        </tr>
        <tr>
          <td>终端型号</td><td>{{ tableData.dmd }}</td>
        </tr>
        <tr>
          <td>铭牌号</td><td>{{ tableData.dnpn }}</td>
        </tr>
        <tr>
          <td>作业名</td><td>{{ tableData.workname }}</td>
        </tr>
        <tr>
          <td>田块类型</td><td>{{ formatterField({},{},tableData.field)  }}</td>
        </tr>
        <tr>
          <td>作业类型</td><td>{{ formatterWtype({},{},tableData.wtype)  }}</td>
        </tr>
        <tr>
          <td>作物类型</td><td>{{ formatterCrop({},{},tableData.crop) }}</td>
        </tr>
        <tr>
          <td>作业面积（亩）</td><td>{{ formatterArea({},{},tableData.area) }}</td>
        </tr>
        <tr>
          <td>作业幅宽（m）</td><td>{{ tableData.wid }}</td>
        </tr>
        <tr>
          <td>作业里程（m）</td><td>{{ tableData.len }}</td>
        </tr>

        <tr>
          <td>合格面积（亩）</td><td>{{ formatterArea({},{},tableData.qarea) }}</td>
        </tr>


        <!--卫星平地-->
        <template v-if="type === typeOption[2]">
          <tr>
            <td>基准高程统计（m）</td><td>{{ tableData.high }}</td>
          </tr>
        </template>
        <!--流量控制-->
        <template v-if="type === typeOption[1]">
          <tr>
            <td>喷洒总量（L）</td><td>{{ tableData.volume }}</td>
          </tr>
          <tr>
            <td>每亩喷洒量（L/亩）</td><td>{{ tableData.volMu }}</td>
          </tr>
        </template>
        <!--深松-->
        <template v-if="type === typeOption[3]">
          <tr>
            <td>平均深度（cm）</td><td>{{ tableData.dep }}</td>
          </tr>
        </template>
        <!--播种-->
        <template v-if="type === typeOption[4]">
          <tr>
            <td>播种行数（行）</td><td>{{ tableData.rowinfo }}</td>
          </tr>
          <tr>
            <td>播种行距（cm）</td><td>{{ tableData.rowSpace }}</td>
          </tr>
          <tr>
            <td>播种株距（cm）</td><td>{{ tableData.plantSpace }}</td>
          </tr>
          <tr>
            <td>播种数（粒）</td><td>{{ tableData.numberinfo }}</td>
          </tr>
        </template>
        <!--变量施肥-->
        <template v-if="type === typeOption[5]">
          <tr>
            <td>经验亩施肥量（kg/亩）</td><td>{{ tableData.set_fer }}</td>
          </tr>
          <tr>
            <td>施肥率</td><td>{{ tableData.decinfo }}</td>
          </tr>
          <tr>
            <td>平均电导率（μS/mm）</td><td>{{ tableData.ele }}</td>
          </tr>
          <tr>
            <td>平均深度（cm）</td><td>{{ tableData.dep }}</td>
          </tr>
        </template>
        <!--侧深施肥-->
        <template v-if="type === typeOption[6]">
          <tr>
            <td>施肥总量（kg）</td><td>{{ formatterArea(null,null,tableData.area) && tableData.fer ? formatterArea(null,null,tableData.area) * tableData.fer : '' }}</td>
          </tr>
          <tr>
            <td>亩施肥量（kg/亩）</td><td>{{ tableData.fer }}</td>
          </tr>
        </template>
        <tr>
          <td>作业时长（h）</td><td>{{ tableData.t }}</td>
        </tr>
        <tr>
          <td>作业开始时间</td><td>{{ tableData.start }}</td>
        </tr>
        <tr>
          <td>作业结束时间</td><td>{{ tableData.end }}</td>
        </tr>
        <tr>
          <td>归属</td><td>{{ belongArea }}</td>
        </tr>
        <tr>
          <td>作业地址</td><td>{{ tableData.endAddress }}</td>
        </tr>
      </table>
    </div>

  </el-drawer>
</template>

<script>
import {
  formatterArea,
  formatterCrop,
  formatterWtype,
  formatterFacCode,
  formatterD_type,
  formatterField
} from '@/views/systemagriculturalmachineryv2/script/table_formatter'
import useVehiclesStore from "@/store/modules/vehicles"
import { mapWritableState } from 'pinia'
export default {
  name: "PrecisionDetails",
  filters: {
    capitalize: function (value) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    }
  }, // 查看详情
  props:{
    tableRowData:{
      type: Object,
      default: () => {}
    },
    visible:{
      type:Boolean,
      default:false,
    },
    size:{
      type:String,
      default:'50%',
    }
  },
  data() {
    return {
      drawer: false,
      tableData:{},
      type:'', //
      typeOption:[1,2,3,4,5,6,7],
      authority:'',
      farm:'',
      belongArea:'',
    };
  },
  computed:{
    ...mapWritableState(useVehiclesStore, ['areaOriginal']),
  },
  watch: {
    areaOriginal(val) {
      if(val.length>0){
        this.init()
      }
    }
  },
  created() {
    this.tableData = Object.assign({},this.tableData, this.tableRowData)
    this.formatDType(this.tableData.wdType)
  },
  mounted() {
    this.init()
  },
  methods:{
    formatterField,
    formatterD_type,
    formatterWtype,
    formatterCrop,
    formatterArea,
    formatterFacCode,
    init(){
      this.drawer = true

      this.areaOriginal.forEach(item=>{
        if(item.orgCode == this.tableData.orgCode) {
          this.belongArea = item.orgFullName
        }
      })
    },
    formatDType(dtype){
      if(dtype == '101' || dtype == '201' || dtype == '602' || dtype == '801') { // 自动驾驶 || 无人驾驶 || 普通作业监测 || 无人机
        this.type = this.typeOption[0]
        return
      }
      if(dtype == '301') { // 流量控制(智能喷雾)
        this.type = this.typeOption[1]
        return
      }
      if(dtype == '401') { // 卫星平地
        this.type = this.typeOption[2]
        return
      }
      if(dtype == '601') { // 深松
        this.type = this.typeOption[3]
        return
      }
      if(dtype == '501') { // 播种
        this.type = this.typeOption[4]
        return
      }
      if(dtype == '702') { // 变量
        this.type = this.typeOption[5]
        return
      }
      if(dtype == '701') { // 侧深
        this.type = this.typeOption[6]
        return
      }
    },
    /**
     * 关闭对话框
     */
    close(){
      this.$emit('close')
    },
  }
}
</script>

<style lang="scss" scoped>
.verticalTable {
  padding: 0 20px;
  font-weight: normal;
  table {
    width: 100%;
    background: #dfe6ec;
    color: #606266;
    font-size: 12px;
    tr {
      td {
        background: white;
        padding: 8px 10px;
        vertical-align: middle;
        text-align: left;
        box-sizing: border-box;
        line-height: 23px;
        &:nth-child(odd) {
          width: 33%;
          font-weight: bold;
        }
      }
    }
  }
}

:deep(.el-drawer__body){
  overflow-y: auto;
}
</style>
