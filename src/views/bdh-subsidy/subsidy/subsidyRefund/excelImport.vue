<template>
  <div v-loading.fullscreen.lock="loading">
    <el-dialog v-if="visible" :title="title" :model-value="visible" width="680px" append-to-body :before-close="cancel"
               :close-on-click-modal="false">
      <el-form ref="importFormRef" :model="importForm" label-width="100px" label-position="left">
        <el-row style="color: red; font-size: small; margin-bottom: 10px">
          请选择年份、所在单位、补贴分类、补贴项目、补贴类型、退回分类后再上传文件
        </el-row>
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="12">
            <el-form-item label="年份" prop="subsidyYear">
              <el-select v-model="importForm.subsidyYear" placeholder="请选择年份">
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                           :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select ref="newOrgSelect" :currentOrgValue="importForm.organizationNo"
                              :defaultType="true" @handleOrgChange="handleOrgChange"
                              style="width: 100%"></new-org-select>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="补贴分类" prop="subsidyClassify">
              <el-select
                v-model="importForm.subsidyClassify"
                placeholder="请选择补贴分类"
                clearable
                disabled
              >
                <el-option
                  v-for="dict in subsidyClassifyOptions"
                  :key="dict.key"
                  :label="dict.value"
                  :value="dict.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴项目" prop="subsidyConfigId">
              <el-select v-model="importForm.subsidyConfigId" placeholder="请选择补贴项目">
                <el-option v-for="dict in querySubsidyProjectList" :key="dict.subsidyConfigId"
                           :label="dict.subsidyItemName" :value="dict.subsidyConfigId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select v-model="importForm.subsidyType" placeholder="请选择补贴类型">
                <el-option v-for="dict in subsidyTypeOptions" :key="dict.code" :label="dict.name"
                           :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退回分类" prop="refundType">
              <el-select v-model="importForm.refundType" placeholder="请选择退回分类">
                <el-option v-for="dict in refundTypeOption" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="模板下载">
          <el-button  v-hasPermi="['subsidyApply:exportTemplate']" type="primary"
                     :underline="false" style="font-size:12px;vertical-align: baseline;"
                     @click="handleDownLoad">下载模板
          </el-button>
        </el-form-item>
        <el-form-item label="上传文件">
          <el-upload v-if="visible" class="upload-demo" :auto-upload="false" ref="upload"
                     :action="uploadFileUrl" :http-request="httpRequest" :limit="1" accept=".xls,.xlsx">
            <el-button :disabled='isDownloadDisabled' type="primary" style="font-size:12px;vertical-align: baseline;">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传xls/xlsx文件，且不超过500kb
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">提 交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="导入失败" v-model="dialogTableVisible" width="800px">
      <el-table border :data="errDataList">
        <el-table-column property="rowIndex" label="行数" width="50" :formatter="valueFormat"></el-table-column>
        <el-table-column property="message" label="错误详细信息" :formatter="valueFormat"></el-table-column>
      </el-table>
    </el-dialog>

    <!-- 导入日志弹窗 -->
    <el-dialog title="导入日志" v-model="importResultDialogVisible" width="600px" :close-on-click-modal="false" :before-close="closeAndTimer">
      <div class="import-log-container result-dialog">
        <div class="log-content">
          <el-scrollbar max-height="400px">
            <div v-for="(log, index) in importLogs" :key="index" class="log-item">
              {{ log }}
            </div>
          </el-scrollbar>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeAndTimer">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import axios from 'axios'
import {
  querySubsidyApply,
} from "@/api/bdh-subsidy/subsidy/apply";
import { importNewApplylog, updateNewApplylog } from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";
import { postForExcel } from "@/api/bdh-subsidy/project/excel"
import {
  subsidyStandardManagegetByOrgs,
} from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
const props = defineProps({
  visible: Boolean,
  yearNoOptions: Array,
  subsidyTypeOptions: Array,
  refundTypeOption: Array,
  title: String,
  exportTemplateUrl: String,
  exportTemplateTitle: String,
  uploadFileUrl: String,
  subsidyClassifyOptions: Array
})

// 使用 ref 定义响应式数据
const loading = ref(false)
const querySubsidyProjectList = ref([])
const dialogTableVisible = ref(false)
const errDataList = ref([])
const importResultDialogVisible = ref(false)
const importLogs = ref([])
// 导入状态检查的计时器
let importStatusTimer = null
// 导入UUID
let importUuid = null
const {proxy} = getCurrentInstance();

// 使用 reactive 定义表单对象，并通过 toRefs 解构
const data = reactive({
  importForm: {
    subsidyClassify:1,
    subsidyYear: String(new Date().getFullYear()) ,
    subsidyType: null,
    refundType:null,
    subsidyConfigId: null,
    organizationNo: null
  }
})

const {importForm} = toRefs(data)

const isDownloadDisabled = computed(() => {
  return !(
      importForm.value.subsidyYear &&
      importForm.value.subsidyType &&
      importForm.value.subsidyConfigId &&
      importForm.value.organizationNo &&
      importForm.value.refundType
  )
})

function querySubsidyConfigtList() {
  subsidyStandardManagegetByOrgs(importForm.value).then((response) => {
    querySubsidyProjectList.value= response.data;

  });
};

// 重置表单数据为初始值
function clearForm() {
  importForm.value = {
    subsidyClassify:1,
    subsidyYear: String(new Date().getFullYear()),
    subsidyType: null,
    subsidyConfigId: null,
    organizationNo: null,
    refundType: null
  }
  proxy.$nextTick(() => {
    querySubsidyConfigtList()
    if(proxy.$refs.newOrgSelect){
      proxy.$refs.newOrgSelect.getCascader('reset')
    }
  })
};



const httpRequest = (param) => {
  errDataList.value = []
  let fileObj = param.file // 相当于input里取得的files
  let fd = new FormData() // FormData 对象
  fd.append('file', fileObj) // 文件对象
  fd.append('param', JSON.stringify(importForm.value)) // 文件对象
  let url =  `${window.VITE_APP_BASE_API}${props.uploadFileUrl}`
  let config = {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }
  loading.value = true
  axios.post(url, fd, config).then((res) => {
    if (res.data.code === 0 && res.data.success === true) {
      proxy.$message.success('文件上传成功')
      proxy.$emit('getList')
      reset()
    } else {
      proxy.$message.error(res.data.msg)
      if (res.data.code !== 40001 && res.data.code == 0) {
        dialogTableVisible.value = true
        errDataList.value = res.data.data
      }
    }
    proxy.$refs.upload.clearFiles()
    loading.value = false
  })
}

function submitForm() {
  proxy.$refs.upload.submit()
};

function cancel() {
  reset()
};

function reset() {
  proxy.resetForm('exportForm')
 proxy.$emit('close')
};

function valueFormat(row, column, cellValue, index) {
  return cellValue == '-1' || cellValue == 'null' ? '' : cellValue
};


function handleDownLoad() {
  proxy.$refs['importFormRef'].validate((valid) => {
    if (valid) {
      postForExcel(props.exportTemplateUrl, {},`${props.exportTemplateTitle}` );
    }
  })
};
//组织机构回显
function handleOrgChange(orgCode) {
  importForm.value.organizationNo = orgCode
};

defineExpose({
  clearForm
})
</script>

<style scoped lang="scss">
/* 导入日志样式 */
.import-log-container {
  padding: 10px;
  
  &.result-dialog {
    height: 400px;
    padding: 0;
  }
}

.log-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  text-align: center;
}

.log-content {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #f5f7fa;
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}

.log-item {
  font-size: 14px;
  line-height: 1.5;
  padding: 5px 0;
  border-bottom: 1px dashed #e6e6e6;
  word-break: break-all;
}
</style>
