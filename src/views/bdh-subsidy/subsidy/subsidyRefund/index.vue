<!--补贴退回管理-->
<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="90px" class="form-line">
        <el-row style="display: flex; flex-wrap: wrap" :gutter="20">
          <el-col :span="6">
            <el-form-item label="年度" prop="subsidyYear">
              <el-select v-model="queryParams.subsidyYear" placeholder="请选择年度" clearable>
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                  :value="parseInt(dict.code)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select @firstTrip="firstTripFun" :checkStrictly="true" :isclearable="false" style="width: 100%"
                :defaultType="true" ref="newOrgSelect" @handleOrgChange="handleOrgChange"
                :currentOrgValue="queryParams.organizationNo"></new-org-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="补贴分类" prop="subsidyClassify">
              <el-select v-model="queryParams.subsidyClassify" @change="handleQuery" disabled placeholder="请选择补贴分类">
                <el-option v-for="dict in subsidyClassifyOptions" :key="dict.key" :label="dict.value"
                  :value="dict.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="补贴项目" prop="subsidyConfigId" label-width="120px">
              <el-select v-model="queryParams.subsidyConfigId" placeholder="请选择补贴项目" clearable>
                <el-option v-for="dict in querySubsidyProjectList" :key="dict.subsidyConfigId"
                  :label="dict.subsidyItemName" :value="dict.subsidyConfigId"
                  v-show="dict.subsidyItemName !== '粮改饲补贴'" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select v-model="queryParams.subsidyType" placeholder="请选择补贴类型" clearable>
                <el-option v-for="dict in subsidyTypeOptions" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核级别" prop="auditLevel">
              <el-select :empty-values="[null, undefined]" v-model="queryParams.auditLevel" placeholder="请选择审核级别">
                <el-option v-for="dict in auditLevelOptions" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核状态" prop="approvalStatusNo">
              <el-select clearable v-model="queryParams.approvalStatusNo" placeholder="请选择审核状态">
                <el-option v-for="dict in auditNoOptions" :key="dict.code" :label="dict.name"
                  :value="parseInt(dict.code)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" align="right">
            <!-- <el-form-item > -->
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['subsidyRefund:insert']" icon="Plus" plain type="primary" @click="handleAdd">新增
        </el-button>
      </el-col>
      <!--导入excel-->
      <el-col :span="1.5">
        <el-button icon="Upload" @click="handleImport">导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['subsidyRefund:logicDeleteByIds', 'subsidyRefund:logicDeleteById']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button plain icon="Check" :disabled="multiple" @click="dialogVisible = true, textarea = ''"
          v-hasPermi="['subsidyRefund:auditPassByIds']">审核
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button plain icon="Check" @click="handlePassAll" v-hasPermi="['subsidyRefund:auditPassAll']">全部审核
        </el-button>
      </el-col>
       <el-col :span="1.5">
        <!--导出excel-->
        <el-button
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['subsidyRefund:exportExcel']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table ref="tables" border :height="tableHeight" :data="refundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="年份" align="center" prop="subsidyYear" />
      <el-table-column label="补贴分类" align="center" prop="subsidyClassify" :formatter="subsidyClassifyFormatter" />
      <el-table-column label="补贴项目" align="center" width="120" prop="subsidyItemName" />
      <el-table-column label="补贴类型" align="center" prop="subsidyType" width="120" :formatter="subsidyTypeFormatter" />
      <el-table-column label="组织机构" align="center" prop="organizationName" width="160px" />
      <el-table-column label="退回分类" align="center" prop="refundType" :formatter="refundTypeOptionFormatter" />
      <el-table-column label="退回人" align="center" prop="farmerName" />
      <el-table-column label="身份证号" align="center" prop="farmerIdNumber" width="160px" />
      <el-table-column label="退返面积" align="center" prop="refundArea" />
      <el-table-column label="退返金额" align="center" prop="refundFee" />
      <el-table-column label="审核状态" align="center" prop="approvalStatusNo" :formatter="auditNoFormat" />
      <el-table-column align="center" label="审核意见" prop="approvalRemark" width="120" />
      <el-table-column align="center" label="当前审核角色名称" prop="currentAuditRoleName" width="130" />
      <el-table-column v-for="(v, i) in tabledata" :key="i" :formatter="formatter" :label="v.label" :prop="v.name"
        align="center" width="120" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="140px">
        <template #default="scope">
        <el-button link type="primary" @click=" openMaterialsDialog(scope.row)"
            v-hasPermi="['subsidyRefund:addFile']" v-if="isShowBtn(scope)">相关材料
          </el-button>
          <el-button link type="primary" @click=" handleDelete(scope.row)"
            v-hasPermi="['subsidyRefund:logicDeleteById']" v-if="isShowBtn(scope)">删除
          </el-button>
          <el-button link type="primary" @click="handleInfo(scope.row)" v-hasPermi="['subsidyRefund:info']">查看详情
          </el-button>
        
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />
    <!-- 选择补贴退返信息 -->
    <el-dialog v-if="open" :close-on-click-modal=false :title="title" v-model="open" append-to-body label-position='top'
      width="1100px">

      <el-form ref="diaQueryForm" :rules="rules" :model="form" label-width="100px">
        <el-row style="display: flex;flex-wrap: wrap;">
          <el-col :span="8">
            <el-form-item label="年份" prop="subsidyYear">
              <el-select v-model="form.subsidyYear" placeholder="请选择年份" @change="diaQueryYearChangeHandler">
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                  :value="parseInt(dict.code)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select @handleOrgChange="handleFormOrgChange" :isclearable="false" ref="newFormOrgSelect"
                :currentOrgValue="form.organizationNo"></new-org-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="补贴分类" prop="subsidyClassify">
              <el-select v-model="form.subsidyClassify" placeholder="请选择补贴分类" clearable disabled>
                <el-option v-for="dict in subsidyClassifyOptions" :key="dict.key" :label="dict.value"
                  :value="dict.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="补贴项目" prop="subsidyConfigId">
              <el-select v-model="form.subsidyConfigId" placeholder="请选择补贴项目">
                <el-option v-for="dict in diaQuerySubsidyProjectList" :key="dict.subsidyConfigId"
                  :label="dict.subsidyItemName" :value="dict.subsidyConfigId"
                  v-show="dict.subsidyItemName !== '粮改饲补贴'"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select v-model="form.subsidyType" placeholder="请选择补贴类型">
                <el-option v-for="dict in subsidyTypeOptions" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户姓名" prop="farmerName">
              <el-input v-model="form.farmerName" placeholder="请输入农户姓名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="farmerIdNumber">
              <el-input v-model="form.farmerIdNumber" placeholder="请输入农户身份证号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退回分类" prop="refundType">
              <el-select v-model="form.refundType" placeholder="请选择退回分类">
                <el-option v-for="dict in refundTypeOption" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" align="right">
            <el-button icon="Refresh" @click="reset">重置</el-button>
            <el-button type="primary" icon="Search" @click="diaHandleQuery">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table ref="formTables" border height="200" :data="formRefundList"
        @selection-change="handleFormSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="姓名" align="center" prop="farmerName" />
        <el-table-column label="身份证号" align="center" prop="farmerIdNumber" width="150" />
        <el-table-column label="补贴作物" align="center" prop="subsidyCropCode" width="100"
          :formatter="subsidyCropCodeFormatter" />
        <el-table-column label="类型" align="center" prop="contractSignType"
          :formatter="contractSignTypeOptionFormatter" />
        <el-table-column label="合同编号" align="center" prop="serialNumber" width="150" />
        <el-table-column label="地块" align="center" prop="landNumber" />
        <el-table-column label="面积" align="center" prop="refundArea" />
        <el-table-column label="实际发放金额合计" align="center" width="100" prop="refundFee"
          :formatter="(row) => formatNumber(row.refundFee)" />
        <el-table-column label="补贴标准1" align="center">
          <el-table-column label="补贴标准（元/亩）" align="center" width="100" prop="subsidyStandard1"
            :formatter="(row) => formatNumber(row.subsidyStandard1)" />
          <el-table-column label="补贴面积（亩）" align="center" prop="subsidyArea1"
            :formatter="(row) => formatNumber(row.subsidyArea1)" />
          <el-table-column label="发放金额（元）" align="center" prop="subsidyFee1"
            :formatter="(row) => formatNumber(row.subsidyFee1)" />
        </el-table-column>
        <el-table-column label="补贴标准2" align="center">
          <el-table-column label="补贴标准（元/亩）" align="center" width="100" prop="subsidyStandard2"
            :formatter="(row) => formatNumber(row.subsidyStandard2)" />
          <el-table-column label="补贴面积（亩）" align="center" prop="subsidyArea2"
            :formatter="(row) => formatNumber(row.subsidyArea2)" />
          <el-table-column label="发放金额（元）" align="center" prop="subsidyFee2"
            :formatter="(row) => formatNumber(row.subsidyFee2)" />
        </el-table-column>
      </el-table>
      <pagination v-show="formTotal > 0" :total="formTotal" v-model:page="form.page" v-model:limit="form.rows"
        @pagination="getFormList" />
      <template #footer>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm" :disabled="formSelectRow.length == 0">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 查看补贴退返信息 -->
    <el-dialog v-if="openInfo" :close-on-click-modal=false :title="titleInfo" v-model="openInfo" append-to-body label-position='top'
      width="1100px">
      <el-table  border height="200" :data="formRefundInfoList"
       >
        <el-table-column label="姓名" align="center" prop="farmerName" />
        <el-table-column label="身份证号" align="center" prop="farmerIdNumber" width="150" />
        <el-table-column label="补贴作物" align="center" prop="subsidyCropCode" width="100"
          :formatter="subsidyCropCodeFormatter" />
        <el-table-column label="类型" align="center" prop="contractSignType"
          :formatter="contractSignTypeOptionFormatter" />
        <el-table-column label="合同编号" align="center" prop="serialNumber" width="150" />
        <el-table-column label="地块" align="center" prop="landNumber" />
        <el-table-column label="面积" align="center" prop="refundArea" />
        <el-table-column label="实际发放金额合计" align="center" width="100" prop="refundFee"
          :formatter="(row) => formatNumber(row.refundFee)" />
        <el-table-column label="补贴标准1" align="center">
          <el-table-column label="补贴标准（元/亩）" align="center" width="100" prop="subsidyStandard1"
            :formatter="(row) => formatNumber(row.subsidyStandard1)" />
          <el-table-column label="补贴面积（亩）" align="center" prop="subsidyArea1"
            :formatter="(row) => formatNumber(row.subsidyArea1)" />
          <el-table-column label="发放金额（元）" align="center" prop="subsidyFee1"
            :formatter="(row) => formatNumber(row.subsidyFee1)" />
        </el-table-column>
        <el-table-column label="补贴标准2" align="center">
          <el-table-column label="补贴标准（元/亩）" align="center" width="100" prop="subsidyStandard2"
            :formatter="(row) => formatNumber(row.subsidyStandard2)" />
          <el-table-column label="补贴面积（亩）" align="center" prop="subsidyArea2"
            :formatter="(row) => formatNumber(row.subsidyArea2)" />
          <el-table-column label="发放金额（元）" align="center" prop="subsidyFee2"
            :formatter="(row) => formatNumber(row.subsidyFee2)" />
        </el-table-column>
      </el-table>
      <div style="margin: 20px 0;">
         <el-divider></el-divider>
      </div>
      <el-row>
        <el-col :span="4">说明必须盖章</el-col>
        <el-col :span="20" style="display: flex;">
          <el-col :span='6' v-for="(item,index) in instructionsList" :key="index" style="margin-right: 10px;">
            <el-link :href="item.fileUrl" target="_blank" type="primary">{{ item.fileName }}</el-link>
          </el-col>
        </el-col>
      </el-row>
      <el-row style="margin-top: 10px;">
        <el-col :span="4">退回说明回单</el-col>
        <el-col :span="20" style="display: flex;">
          <el-col :span='6' v-for="(item,index) in bankFileList" :key="index" style="margin-right: 10px;">
            <el-link :href="item.fileUrl" target="_blank" type="primary">{{ item.fileName }}</el-link>
          </el-col>
        </el-col>
      </el-row>
      <template #footer>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelInfo">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 相关材料弹窗 -->
    <el-dialog title="相关材料" v-model="materialsDialogVisible" :close-on-click-modal="false" width="800px" append-to-body
      @closed="resetMaterialsForm">
      <el-form ref="materialsForm" :model="materialsForm" label-width="180px">
        <el-form-item label="说明（必须盖章）：" prop="instructionsList"
         >
          <fileUpload :urlApi="urlApi" :limit="9" :fileType="fileType" v-model="materialsForm.instructionsList"
            @fileUploadChange="fileUploadinstructionsList"></fileUpload>
        </el-form-item>
        <el-form-item label="退回银行回单：" prop="bankFileList"
          >
          <fileUpload :urlApi="urlApi" :limit="9" :fileType="fileType" v-model="materialsForm.bankFileList"
            @fileUploadChange="fileUploadbankFileList"></fileUpload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMaterialsForm">提 交</el-button>
          <el-button @click="materialsDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="审核操作" :closeOnClickModal=false v-model="dialogVisible" width="30%" top>
      <span>备注：</span>
      <el-input type="textarea" :rows="2" placeholder="请输入备注" v-model="textarea">
      </el-input>

      <template #footer>
        <div slot="footer" class="dialog-footer">
          <el-button type="danger" @click="handleNoAudit"
            v-hasPermi="['subsidyBudgetAudit:auditCancelByIds']">审核拒绝</el-button>
          <el-button type="primary" @click="handleAudit"
            v-hasPermi="['subsidyBudgetAudit:auditPassByIds']">审核通过</el-button>
        </div>
      </template>
    </el-dialog>

    <excel-import ref="excelImportRef" :visible="excelImportOpen" title="导入" @close="handleClose" @refresh="getList"
      :subsidyClassifyOptions="subsidyClassifyOptions" :yearNoOptions="yearNoOptions"
      :subsidyTypeOptions="subsidyTypeOptions" :refundTypeOption="refundTypeOption" :uploadFileUrl="uploadFileUrl" :exportTemplateUrl="exportTemplateUrl"
      :exportTemplateTitle="exportTemplateTitle"></excel-import>
  </div>
</template>

<script setup name="/subsidy/budgetAudit/queryByPage">
import { selectDictLabel } from "@/utils/cop";
import { getDicts } from "@/api/bdh-subsidy/system/dict/data";
import {postForExcel} from "@/api/bdh-subsidy/project/excel.js";
import {
  subsidyStandardManagegetByOrgs,
} from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";
import exceldownload from "@/views/bdh-subsidy/components/exceldownload/index";
import { getMaxAuditLevel } from "@/api/bdh-subsidy/landappinitialize/permit";
import { Levelfun } from "@/utils/cop";
import {
  listRefund,refunAdddFile, auditPassAll, auditPass, auditCancel, delRefund, delRefunds, getRefundDetail,addRefundInsert,refundInfo
} from "@/api/bdh-subsidy/subsidy/refund";
import axios from "axios";
import { ref } from "vue";
import excelImport from './excelImport.vue'
import fileUpload from "@/components/FileUpload/index.vue";
import { getSelectSubsidyPermission } from "@/api/bdh-subsidy/subsidy/subsidyAnnounceConfig.js";
const { proxy } = getCurrentInstance();
// ==================== 基础配置 ====================
const urlApi = ref(import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY);
const fileType = ["pdf", "docx", "doc", "png", "jpg", "jpeg"]; // 支持的文件类型

// ==================== 页面布局相关 ====================
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏DOM元素
const showSearch = ref(true); // 显示搜索条件

// ==================== 表格数据相关 ====================
const refundList = ref([]); // 补贴退回表格数据
const total = ref(0); // 总条数
const ids = ref([]); // 选中数组
const single = ref(true); // 非单个禁用
const multiple = ref(true); // 非多个禁用
const hasAudit = ref(0); // 已审核数量

// ==================== 新增弹窗相关 ====================
const open = ref(false); // 是否显示新增弹出层
const title = ref(""); // 新增弹出层标题
const formRefundList = ref([]); // 新增弹窗表格数据
const formTotal = ref(0); // 新增弹窗表格总数
const formSelectRow = ref([]); // 新增表格里选中的数据

// ==================== 查看详情弹窗相关 ====================
const openInfo = ref(false); // 是否显示详情弹窗
const titleInfo = ref(''); // 详情弹窗标题
const formRefundInfoList = ref([]); // 详情表格数据
const instructionsList = ref([]); // 说明附件列表
const bankFileList = ref([]); // 银行回单附件列表

// ==================== 相关材料弹窗相关 ====================
const materialsDialogVisible = ref(false); // 相关材料弹窗显示状态
const materialsForm = ref({
  instructionsList: [], // 说明附件
  bankFileList: [] // 银行回单附件
});
const descriptionFileUrls = ref([]); // 说明文件URL列表
const bankReceiptFileUrls = ref([]); // 银行回单文件URL列表
const rowData = ref({}); // 当前操作的行数据

// ==================== 审核相关 ====================
const dialogVisible = ref(false); // 审核弹窗显示状态
const textarea = ref(''); // 审核备注

// ==================== 导入导出相关 ====================
const excelImportOpen = ref(false); // 导入弹窗显示状态
const uploadFileUrl = ref("/" + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + "/subsidy/subsidyRefund/importExcel");
const exportTemplateUrl = ref('/' + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + '/subsidy/subsidyRefund/exportTemplate');
const exportTemplateTitle = ref('补贴退回导入模版');

// ==================== 字典选项相关 ====================
const subsidyClassifyOptions = ref([]); // 补贴分类选项
const subsidyTypeOptions = ref([]); // 补贴类型选项
const subsidyCropCodeOptions = ref([]); // 补贴作物选项
const yearNoOptions = ref([]); // 年度选项
const refundTypeOption = ref([]); // 退回类型选项
const contractSignTypeOption = ref([]); // 合同签订类型选项
const auditNoOptions = ref([]); // 审核状态选项
const approverNoOptions = ref([]); // 审批人选项
const bankNameList = ref([]); // 银行名称列表

// ==================== 补贴项目相关 ====================
const querySubsidyProjectList = ref([]); // 查询页面补贴项目列表
const diaQuerySubsidyProjectList = ref([]); // 弹窗页面补贴项目列表

// ==================== 审核级别相关 ====================
const maxAuditLevel = ref(1); // 最大审核级别
const tabledata = Levelfun(8); // 审核级别表格数据
// 审核级别选项（固定配置）
const auditLevelOptions = ref([{
  code: '',
  name: '全部'
}, {
  code: '0',
  name: '一级审核'
}, {
  code: '1',
  name: '二级审核'
}, {
  code: '2',
  name: '三级审核'
}, {
  code: '3',
  name: '四级审核'
}, {
  code: '4',
  name: '五级审核'
}, {
  code: '5',
  name: '六级审核'
}, {
  code: '6',
  name: '七级审核'
}, {
  code: '7',
  name: '八级审核'
}]);
const data = reactive({
  // 查询参数
  queryParams: {
    page: 1,
    rows: 10,
    subsidyYear: String(new Date().getFullYear()),
    organizationNo: null,
    subsidyConfigId: null,
    subsidyType: null,
    subsidyClassify: 1,
    auditLevel: '',
    approvalStatusNo: 0,
  },
  // 表单参数
  form: {
    subsidyClassify: 1,
    subsidyType: null,
    farmerName: null,
    farmerIdNumber: null,
    refundType: null,
    subsidyConfigId: null,
    subsidyYear: String(new Date().getFullYear()),
    organizationNo: null,
    page: 1,
    rows: 10,
  },
  // 表单校验
  rules: {
    subsidyType: [{
      required: true,
      message: "请选择补贴类型",
      trigger: "change"
    }],
    subsidyBudgetId: [{
      required: true,
      message: "请选择补贴预算名称",
      trigger: "change"
    }],
    subsidyConfigId: [{
      required: true,
      message: "请选择补贴项目",
      trigger: "change"
    }],
    subsidyYear: [{
      required: true,
      message: "请选择年份",
      trigger: "change"
    }],
    organizationNo: [{
      required: true,
      message: "请选择组织机构",
      trigger: "change"
    }],
    subsidyClassify: [{
      required: true,
      message: "请选择补贴分类",
      trigger: "change"
    }],
    farmerIdNumber: [{
      required: true,
      message: "请输入身份证号",
      trigger: "blur"
    }],
    refundType: [{
      required: true,
      message: "请选择退回分类",
      trigger: "change"
    }],
  },
});
const { queryParams, form, rules } = toRefs(data);
watch(() => queryParams.value.subsidyYear, (newVal, oldVal) => {
  queryParams.subsidyConfigId = null
  queryYearChangeHandler()
}, { immediate: true });
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 280
    : window.innerHeight - 280;
});
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
  getDicts("sys_subsidy_crop_code").then(response => {
    subsidyCropCodeOptions.value = response.data;
  });
  getDicts("sys_subsidy_type").then(response => {
    subsidyTypeOptions.value = response.data;
  });
  getDicts("year_cd").then((response) => {
    yearNoOptions.value = response.data;
  });
  getDicts("aproval_status_no").then(response => {
    approverNoOptions.value = response.data;
    auditNoOptions.value = response.data
  });
  getDicts("refund_type").then((response) => {
    refundTypeOption.value = response.data;
  });
  getDicts("bank_name").then(response => {
    bankNameList.value = response.data;
  });
  //类型
  getDicts("contract_sign_type").then((response) => {
    contractSignTypeOption.value = response.data;
  });

});
/** 查询补贴退回表列表 */
function getList() {
  refundList.value = []
  listRefund(queryParams.value).then(response => {
    total.value = response.data.total;
    refundList.value = response.data.records
    // maxAuditLevel.value = response.data.maxAuditLevel;
    // let params = {
    //   organizationNo: queryParams.value.organizationNo,
    //   billType: 256
    // }
    // getMaxAuditLevel(params).then((response) => {
    //   if (response.data) {
    //     tabledata.value = Levelfun(response.data)
    //   } else {
    //     tabledata.value = 0
    //   }
    //   proxy.$nextTick(() => {
    //     proxy.$refs.tables.doLayout();
    //   })
    // })
  })
};
// 取消按钮
function cancel() {
  open.value = false;
  reset();
};
function cancelInfo(){
  openInfo.value = false;
  resetInfo();
}
// 表单重置
function reset() {
  form.value = {
    subsidyClassify: 1,
    subsidyType: null,
    farmerName: null,
    farmerIdNumber: null,
    refundType: null,
    subsidyConfigId: null,
    subsidyYear: String(new Date().getFullYear()),
    organizationNo: null,
    page: 1,
    rows: 10,
  };
  formSelectRow.value = [];
  formRefundList.value = [];
  proxy.resetForm("diaQueryForm");
  proxy.$refs?.newFormOrgSelect?.getCascader('reset');
};
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
};
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  proxy.$refs.newOrgSelect.getCascader('reset');
  handleQuery();
};

function diaHandleQuery() {
  form.value.page = 1;
  getFormList();
}
//获取新增里面的表格数据
function getFormList() {
  proxy.$refs["diaQueryForm"].validate((valid) => {
    if (valid) {
      formRefundList.value = [];
      getRefundDetail(form.value).then(response => {
        formRefundList.value = response.data.records || [];
        formTotal.value = response.data.total || 0;
      })
    }
  })
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.subsidyRefundId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
  hasAudit.value = selection.filter((item) => item.auditAFlag != null && item.auditAFlag != '');
};

// 弹窗表格多选框选中数据
function handleFormSelectionChange(selection) {
  formSelectRow.value  = selection
};
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "选择补贴退返信息";
  diaQueryYearChangeHandler()
};


//上传说明文件
let fileUploadinstructionsList = (value) => {
  if (Array.isArray(value)) {
    descriptionFileUrls.value = value;
    materialsForm.value.instructionsList = value;
    console.log(1,descriptionFileUrls.value,materialsForm.value.instructionsList)
    // 确保表单验证能够检测到变化
    nextTick(() => {
      if (proxy.$refs.materialsForm) {
        proxy.$refs.materialsForm.validateField('instructionsList');
      }
    });
  }
}

//上传退回银行回单
let fileUploadbankFileList = (value) => {
  if (Array.isArray(value)) {
    bankReceiptFileUrls.value = value;
    materialsForm.value.bankFileList = value;
    // 确保表单验证能够检测到变化
    nextTick(() => {
      if (proxy.$refs.materialsForm) {
        proxy.$refs.materialsForm.validateField('bankFileList');
      }
    });
  }
}
//查看详情
function handleInfo(row) {
  resetInfo();
  refundInfo(row.subsidyRefundId).then((response) => {
    // 先显示弹窗
    openInfo.value = true;
    titleInfo.value = "补贴返回信息" + '('+selectDictLabel(refundTypeOption.value, row.refundType)+ ')';
    
    if (response.data) {
      // 设置表格数据
      formRefundInfoList.value = response.data.subList.map(item => {
     return {
        ...item,
        farmerName: response.data.farmerName,
        farmerIdNumber: response.data.farmerIdNumber,
        contractSignType: response.data.contractSignType
     };
   })|| [];
      // 设置附件数据
      instructionsList.value = response.data.instructionsList || [];
      bankFileList.value = response.data.bankFileList || [];
    }
  }).catch(error => {
    proxy.$modal.msgError("获取详情失败");
  });
};

// 重置查看详情表单
function resetInfo() {
  formRefundInfoList.value = [];
  instructionsList.value = [];
  bankFileList.value = [];
}
// 打开相关材料弹窗
function openMaterialsDialog(row) {
  rowData.value = row;
  if (row.auditAFlag != null && row.auditAFlag != '') return proxy.$modal.msgError("只允许操作'未发生过审核操作'的数据");
  
  // 先重置表单并显示弹窗
  resetMaterialsForm();
  materialsDialogVisible.value = true;
  
  // 获取数据
  refundInfo(row.subsidyRefundId).then((response) => {
    console.log('refundInfo response:', response.data); // 调试日志
    
    // 使用 nextTick 确保弹窗 DOM 渲染完成
    nextTick(() => {
      // 添加小延迟确保 FileUpload 组件完全初始化
      setTimeout(() => {
        // 转换数据格式
        if (response.data && response.data.instructionsList && Array.isArray(response.data.instructionsList)) {
          const instructionsList = response.data.instructionsList.map((item, index) => ({
            name: item.fileName || `file_${index}`,
            url: item.fileUrl,
            uid: item.fileId || `instruction_${Date.now()}_${index}`,
          }));
          console.log('Setting instructionsList:', instructionsList);
          materialsForm.value.instructionsList = instructionsList;
          descriptionFileUrls.value = instructionsList;
        }
        
        if (response.data && response.data.bankFileList && Array.isArray(response.data.bankFileList)) {
          const bankFileList = response.data.bankFileList.map((item, index) => ({
            name: item.fileName || `file_${index}`,
            url: item.fileUrl,
            uid: item.fileId || `bank_${Date.now()}_${index}`,
          }));
          console.log('Setting bankFileList:', bankFileList);
          materialsForm.value.bankFileList = bankFileList;
          bankReceiptFileUrls.value = bankFileList;
        }
        
        console.log('Final materialsForm after timeout:', materialsForm.value);
      }, 0);
    });
  }).catch(error => {
    console.error('refundInfo error:', error);
    proxy.$modal.msgError("获取相关材料失败");
  });
}

// 重置相关材料表单
function resetMaterialsForm() {
  materialsForm.value = {
    instructionsList: [],
    bankFileList: []
  };
  descriptionFileUrls.value = [];
  bankReceiptFileUrls.value = [];
  // 注释掉 resetFields，避免在设置数据时被重置
  // if (proxy.$refs.materialsForm) {
  //   proxy.$refs.materialsForm.resetFields();
  // }
}

// 提交相关材料表单
function submitMaterialsForm() {
  proxy.$refs.materialsForm.validate((valid) => {
    if (valid) {
      // 检查文件是否已上传
      if (materialsForm.value.instructionsList?.length > 0 &&
        materialsForm.value.bankFileList?.length > 0) {
        refunAdddFile({
          instructionsList: materialsForm.value.instructionsList.map(item => ({
            fileName: item.fileName || item.name,
            fileType: item.fileType || (item.name ? item.name.slice(item.name.lastIndexOf('.') + 1) : ''),
            fileUrl: item.fileUrl || item.url,
          })),
          bankFileList: materialsForm.value.bankFileList.map(item => ({
            fileName: item.fileName || item.name,
            fileType: item.fileType || (item.name ? item.name.slice(item.name.lastIndexOf('.') + 1) : ''),
            fileUrl: item.fileUrl || item.url,
          })),
          subsidyRefundId: rowData.value.subsidyRefundId
        }).then(res => {
          if (res.code == 0) {
            proxy.$modal.msgSuccess("相关材料上传成功");
            // 关闭弹窗
            materialsDialogVisible.value = false;
          } else {
            proxy.$modal.msgError(res.msg);
          }
        })
      } else {
        proxy.$modal.msgError("请上传必要的文件");
      }
    }
  });
}

//查看附件页面跳转
function handleLookAnnex(row) {
  // 跳转页面预览
  window.open(row.annexUrl);
};
//下载附件
function handleDownLoad(row) {
  let params = {
    fileUrl: row.annexUrl
  }
  axios({
    method: "post",
    url: window.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + "/subsidy/budgetAudit/downLoadFile",
    responseType: "blob",
    params: params,
    data: params,
  }).then((res) => {
    const blob = new Blob([res.data], {
      type: res.headers["content-type"] ? res.headers["content-type"] : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    }); // res就是接口返回的文件流了
    // console.log(blob.type)
    if (blob.type && blob.type.startsWith("application/json")) {
      // console.log(blob.text())
      const reader = new FileReader()
      reader.onload = function () {
        const { msg } = JSON.parse(reader.result)//此处的msg就是后端返回的msg内容
        return proxy.$modal.msgError(msg);
      }
      return reader.readAsText(blob)
    }
    //对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
    //IE10以上支持blob但是依然不支持download
    //支持a标签download的浏览器

    if ('download' in document.createElement('a')) {
      console.log('download in chrome');
      const link = document.createElement('a');//创建a标签
      link.download = row.annexName ? row.annexName : "export.png";//a标签添加属性
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      document.body.appendChild(link);
      link.click();//执行下载
      URL.revokeObjectURL(link.href); //释放url
      document.body.removeChild(link);//释放标签
    } else {
      console.log('download in IE');
      let objectUrl = URL.createObjectURL(blob);
      window.location.href = objectUrl;
    }
  });
};

/** 提交按钮 */
function submitForm() {
  proxy.$refs["diaQueryForm"].validate((valid) => {
    if (valid) {
      let params = {
       ...form.value,
        subIdList: formSelectRow.value.map(item => item.subsidyDetailSubId),
      }

      addRefundInsert(params).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
      });
    }
  })
};
/** 删除按钮操作 */
function handleDelete(row) {
  if (hasAudit.value.length > 0  ||(row.auditAFlag != null && row.auditAFlag != '') ) return proxy.$modal.msgError("只允许删除'未发生过审核操作'的数据");
  const subsidyRefundIds = row.subsidyRefundId || ids.value;
  proxy.$modal.confirm('是否确认删除该数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function () {
    return Array.isArray(subsidyRefundIds) ? delRefunds(subsidyRefundIds) : delRefund([subsidyRefundIds]);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  })
};
function firstTripFun() {
  getSubsidyClassifyOptions()
}
const getSubsidyClassifyOptions = () => {
  getSelectSubsidyPermission().then((response) => {
    subsidyClassifyOptions.value = response.data;
    queryYearChangeHandler();
    getList();
  });
};
function handleOrgChange(orgCode, type) {
  queryParams.value.organizationNo = orgCode;
};
function handleFormOrgChange(orgCode, type) {
  form.value.organizationNo = orgCode;
};

function queryYearChangeHandler() {
  const params = {}
  if (!queryParams.value.subsidyYear) {
    querySubsidyProjectList.value = []
    return
  }
  queryParams.value.subsidyConfigId = null
  params.subsidyClassify = queryParams.value.subsidyClassify;
  params.subsidyYear = queryParams.value.subsidyYear
  subsidyStandardManagegetByOrgs(params).then(response => {
    querySubsidyProjectList.value = response.data;
  });
};
function diaQueryYearChangeHandler() {
  const params = {}
  if (!form.value.subsidyYear) {
    diaQuerySubsidyProjectList.value = []
    return
  }
 form.value.subsidyConfigId = null
  params.subsidyClassify = form.value.subsidyClassify
  params.subsidyYear = form.value.subsidyYear
  subsidyStandardManagegetByOrgs(params).then(response => {
    diaQuerySubsidyProjectList.value = response.data;
  });
};
function isShowBtn(scope) {
  return scope.row.approvalStatusNo == 0;
};
/** 审核通过按钮操作 */
function handleAudit() {
  const rationPlanIds = ids.value;
  auditPass({ rationPlanIds, approvalRemark: textarea.value }).then((res) => {
    if (res.code === 0) {
      getList();
      proxy.$modal.msgSuccess("操作成功");
    }
  })
  dialogVisible.value = false
};
/** 审核拒绝按钮操作 */
function handleNoAudit() {
  if (textarea.value == '') return proxy.$modal.msgError("请填写备注");
  const rationPlanIds = ids.value;
  auditCancel({ rationPlanIds, approvalRemark: textarea.value }).then((res) => {
    if (res.code === 0) {
      getList();
      proxy.$modal.msgSuccess("操作成功");
    }
  })
  dialogVisible.value = false
};
//导入
function handleImport() {
  proxy.$refs.excelImportRef.clearForm();
  excelImportOpen.value = true;
};
//关闭导入模板的弹窗
function handleClose(modal) {
  excelImportOpen.value = false
};
//表格的 Formatter
function subsidyTypeFormatter(row, colmn) {
  return selectDictLabel(subsidyTypeOptions.value, row.subsidyType != null && row.subsidyType != undefined ? row.subsidyType.toString() : "");
};
function subsidyCropCodeFormatter(row, colmn) {
  return selectDictLabel(subsidyCropCodeOptions.value, row.subsidyCropCode != null && row.subsidyCropCode != undefined ? row.subsidyCropCode.toString() : "");
};
function approvalStatusNoFormatter(row, column) {
  return selectDictLabel(approverNoOptions.value, row.approvalStatusNo != null && row.approvalStatusNo != undefined ? row.approvalStatusNo.toString() : "");
};
function auditNoFormat(row, column) {
  return selectDictLabel(auditNoOptions.value, row.approvalStatusNo);
};
function refundTypeOptionFormatter(row, column) {
  return selectDictLabel(refundTypeOption.value, row.refundType);
};
function contractSignTypeOptionFormatter(row, column) {
  return selectDictLabel(contractSignTypeOption.value, row.contractSignType);
};
function subsidyClassifyFormatter(row, column) {
  if (subsidyClassifyOptions.value.length > 0) {
    let value = "";
    subsidyClassifyOptions.value.forEach((v) => {
      if (v.key == row.subsidyClassify) {
        value = v.value;
      }
    });
    return value;
  }
};
function formatter(row, column, cellValue, index) {
  const condition = ['approvalStatusNo', 'auditAFlag', 'auditBFlag', 'auditCFlag', 'auditDFlag', 'auditEFlag', 'auditFFlag', 'auditGFlag', 'auditHFlag'];
  if (condition.includes(column.property)) {
    return selectDictLabel(auditNoOptions.value, row[column.property] !== null && row[column.property] !== undefined ? row[column.property] : "")
  } else {
    return row[column.property]
  }
};

// 数字格式化函数
function formatNumber(value) {
  return value != undefined && value != null
    ? value.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}

// 全部审核
function handlePassAll() {
  proxy.$modal
    .confirm("此操作将全部审核通过, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      auditPassAll(newqueryParams.value)
        .then(() => {
          proxy.$message({
            type: "success",
            message: "全部审核完成!",
          });
          getList();
        })
        .catch((err) => {});
    })
    .catch(() => {});
}
function handleExport() {
  postForExcel('/' + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + '/subsidy/subsidyRefund/exportExcel', queryParams.value, '补贴退回管理');
}
</script>
<style lang="scss" scoped>
:deep(.el-form--inline .el-form-item) {
  display: flex;
}

:deep {
  .pagination-container .el-pagination {
    margin-right: 20px;
  }

  .el-input-number--medium {
    width: 100% !important;
  }

  .el-table__fixed {
    height: calc(100% - 14px) !important;
  }
}
</style>
