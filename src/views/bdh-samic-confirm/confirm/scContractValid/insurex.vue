<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model="queryParams"  :rules="rules" ref="queryRef" v-show="showSearch" class="form-line">
                    <el-row :gutter="24">

                        <el-col :span="6">
                            <el-form-item label="年份" prop="statYear">
                                <el-select v-model="queryParams.statYear" >
                                    <el-option
                                        v-for="item in statYear"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="机构" prop="orgCode" label-width="80px" >
                                <org-tpl apiUrl="bdh-samic-confirm-api"
                                    ref="orgCodeRef"
                                    :defaultOrgCode="queryParams.orgCode"
                                    placeholderText="请选择组织机构"
                                    @handleOrgCode="handleOrgCodeQuery"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" >
                            <el-form-item label="合同ID" prop="contactId">
                                <el-input
                                    v-model="queryParams.contactId"
                                    placeholder="请输入合同编号"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" >
                            <el-form-item label="姓名" prop="personName">
                                <el-input
                                    v-model="queryParams.personName"
                                    placeholder="姓名"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>


                    <el-row :gutter="24">
                        <el-col :span="6" >
                            <el-form-item label="身份证" prop="queryCertNo">
                                <el-input
                                    v-model="queryCertNo"
                                    placeholder="请输入身份证号"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" >
                            <el-form-item label="合同编号" prop="serialNumber" label-width="80px">
                                <el-input
                                    v-model="queryParams.serialNumber"
                                    placeholder="请输入合同编号"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" align="right" >
                            <!-- <el-button icon="Refresh" @click="resetQuery">重置</el-button>-->
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <!--                                <el-button icon="Download" @click="handleExport">导出</el-button>-->
                        </el-col>
                    </el-row>

                </el-form>
            </el-collapse-transition>
        </div>


        <div>
            <el-table :data="pageData" :height="tableHeight">
                <el-table-column label="组织机构" align="center">
                    <template #default="scope">
                        <div :title="scope.row.orgCode">
                            {{scope.row.orgName}}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="合同ID" align="center" prop="contractId" />
                <el-table-column label="合同编号" align="center" prop="serialNumber" />
                <el-table-column label="是否确认" align="center" :formatter="isConfirm" width="90"/>


                <el-table-column label="合同面积" align="center" prop="area" width="90"/>
                <el-table-column label="确认面积" align="center" prop="insureArea" width="90"/>
                <el-table-column label="合同地块数量" align="center" prop="contractPlotCount" width="110"/>
                <el-table-column label="确认地块数量" align="center" prop="bindPlotCount" width="110"/>

                <el-table-column label="白名单地块数量" align="center" prop="whitePlotCount" width="120"/>

                <el-table-column label="姓名" align="center" prop="farmerName" width="80"/>
                <el-table-column label="身份证" align="center" prop="farmerIdNumber" width="120"/>
                <el-table-column label="签字时间" align="center" width="160">
                    <template v-slot="scope">
                        <div>{{scope.row.scInsureConfInfo.createTime}}</div>
                    </template>
                </el-table-column>

                <el-table-column label="稽核结果" align="center" width="60">
                    <template v-slot="scope">
                        <div v-html="formatterSuccess(scope.row)" ></div>
                    </template>
                </el-table-column>


                <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="120">
                    <template #default="scope">

                        <el-tooltip class="item" effect="dark" content="详情" placement="top-start">
                            <el-button link icon="data-line" @click="showDetail(scope.row)"/>
                        </el-tooltip>

                        <el-popconfirm title="确认补录地块么？" @confirm="addPlotY(scope.row)">
                            <template #reference>
                                <el-button link icon="plus" />
                            </template>
                        </el-popconfirm>

                        <el-popconfirm title="确认同步数据到一体化平台么？" @confirm="syncDataX(scope.row)">
                            <template #reference>
                                <el-button link icon="refresh"/>
                            </template>
                        </el-popconfirm>


                      <el-popconfirm title="确认重置数据?系统将执行如下操作：1、删除签字记录、2、删除一体化记录" @confirm="resetData(scope.row)">
                        <template #reference>
                          <el-button link icon="warning"  type="danger"></el-button>
                        </template>
                      </el-popconfirm>

                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total>0"
                :total="total"
                v-model:page="queryParams.page"
                v-model:limit="queryParams.rows"
                @pagination="handleQuery"
            />
        </div>



        <el-dialog :title="infoTitile" v-model="infoOpen" append-to-body width="800">
            <el-table :data="currentInfo" >
                <el-table-column label="合同地块" align="center" prop="landNumber" />
                <el-table-column label="合同面积" align="center" prop="area" />
<!--                <el-table-column label="确认面积" align="center" prop="insureArea"/>-->
                <el-table-column label="是否确认" align="center">
                    <template v-slot="scope">
                        <div v-html="formatterBind(scope.row)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="是否白名单" align="center" >
                    <template v-slot="scope">
                        <div v-html="formatterWhite(scope.row)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="白名单录入时间" align="center" prop="createTime" width="160"/>
             </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="infoOpen = false">关 闭</el-button>
                </div>
            </template>
        </el-dialog>
        <!--
        <div>
            <div v-for="(contract,index) in validList" :key="index" style="border:1px solid darkgray;padding:0 5px 5px 5px">
                <div style="border-bottom:1px solid darkgray;padding:0 5px 5px 5px">
                    编号：{{contract.contractId}}&nbsp;&nbsp;
                    | 是否确认：{{isConfirm(contract.scInsureConfInfo)}}&nbsp;&nbsp;
                </div>
                <div style="border-bottom:1px solid darkgray;padding:0 5px 5px 5px">
                    合同地块数量：{{contract.contractPlotCount}}&nbsp;&nbsp;
                    | 确认地块数量：{{contract.bindPlotCount}}&nbsp;&nbsp;
                    | 白名单地块数量:{{ contract.whitePlotCount}}&nbsp;&nbsp;
                    | 合同面积: {{contract.area}}&nbsp;&nbsp;
                    | 确认面积: {{contract.insureArea}}
                </div>
                <div style="border-bottom:1px solid darkgray;padding:0 5px 5px 5px">
                    稽核结果：{{ formatterSuccess(contract.success)}}&nbsp;&nbsp;
                    | 序号：{{contract.serialNumber}}&nbsp;&nbsp;
                    | 姓名：{{contract.personName}}&nbsp;&nbsp;
                    | 身份证：{{decIdCard(contract.farmerIdNumber)}}&nbsp;&nbsp;
                    | message：{{contract.message}}
                </div>
                <div style="padding-top: 20px;" v-html="formatterPlot(contract.contractChargePlans)"></div>
            </div>
        </div>
        -->
    </div>
</template>

<script setup name="/confirm/valid/insure/pageX">
import { getCurrentInstance, onMounted } from "vue"
import { getDicts } from "@/api/bdh-samic-confirm/system/dict/data"
import {
  contactSync,
  addPlot,
  addWhitePlot,
  insurePageX,
  signReset
} from "@/api/bdh-samic-confirm/confirm/scContractValid"
const env = import.meta.env
import { ElMessageBox } from 'element-plus';
import orgTpl from "@/views/bdh-samic-confirm/components/OrgSelect/index.vue"

const { proxy } = getCurrentInstance()
const validList = ref([])
const loading = ref(true)
const total = ref(0)
const title = ref("")
const searchHeight = ref(0) // 搜索栏高度
const tableHeight = ref(400) // 表格高度
const searchDom = ref() // 搜索栏dom
const showSearch = ref(true)
const infoTitile =ref("")
const infoOpen = ref(false)

const statYear = ref([])
const queryCertNo = ref("")
const orgCode = ref([])
const contactId = ref([])
const maskCertNo = ref("")
const pageData = ref([])
const currentInfo=ref(null)

const data = reactive({
    form: {},
    queryParams: {
        statYear: null,
        certNo: null,
        orgCode: null,
        contactId: null,
        personName:null,
        serialNumber:null,
        page:1,
        rows:10
    },
    idNum:null,
    rules: {
        statYear: {
            required: true,
            message: "请选择年份",
            trigger: "change",
        }
    },
})


const { queryParams,rules} = toRefs(data)

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 190
        : window.innerHeight - 190
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 190
        : window.innerHeight - 190
});

/** 查询地块白名单列表 */
function getList() {
  loading.value = true
  proxy.$refs["queryRef"].validate(valid => {
    if (valid) {
      if(queryCertNo.value!=='') {
        maskCertNo.value = btoa(queryCertNo.value)
        queryParams.value.certNo = maskCertNo.value
      }else{
        maskCertNo.value = ""
        queryParams.value.certNo =""
      }
      insurePageX(queryParams.value).then(response => {
        pageData.value = response.data.records;
        total.value = response.data.total
      });
    }
  });
}
function resetData(row){
  let scInsureConfInfo = row.scInsureConfInfo;
  let insureConfId = scInsureConfInfo.insureConfId;
  let contactId = scInsureConfInfo.contactId;
  let param = {
    "contactId":contactId,
    "insureConfId":insureConfId,
    "mark":1,
    "remark":"重置"
  }
  signReset(param).then(response => {
    ElMessageBox.alert(response.msg, '消息');
  });
}
function decIdCard(row){
    let encData = row.farmerIdNumber;
    let normalText  = "";
    try {
        normalText = decodeURIComponent(atob(encData).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
    } catch (e) {
        normalText = '输入的文本不是有效的mask编码';
    }
    return normalText;
}


function formatterResStatus(row){
    let resStatus = row.resStatus
    if(resStatus===1){
        return "正常";
    }else{
        return "<font color='red'>没有</font>";
    }

}


function formatterYN(row){
    let status = row.bind
    if(status===1){
        return "是";
    }else{
        return "<font color='red'>否</font>";
    }
}

function formatterWhite(row){
    let white = row.whiteStatus
    if(white===1){
        return "是";
    }else{
        return "<font color='red'>否</font>";
    }
}

function formatterBind(row){
    let bind = row.confirmStatus
    if(bind===1){
        return "是";
    }else{
        return "<font color='red'>否</font>";
    }
}


function formatterValid(status){
    if(status===1){
        return "存在";
    }else{
        return "<font color='red'>不存在</font>";
    }
}

function formatterSuccess(row){
    let status = row.success
    if(status===1){
        return "成功";
    }else{
        return "<font color='red'>失败</font>";
    }
}
function isConfirm(row){
    let insure = row.scInsureConfInfo
    let ret = "否";
    let insureConfId =-1;
    if(insure!=null){
        insureConfId = insure.insureConfId;
    }
    if(insureConfId>1){
        ret = "是";
    }
    return ret;
}
// 表单重置
function reset() {
    queryParams.value = {
        statYear: null,
        certNo: null,
        orgCode: null,
        contactId: null
    }
}

function showDetail(row){
    currentInfo.value = row.contractPlotConfirmVos;
    infoTitile.value="详情";
    infoOpen.value = true;
}

function formatterPlot(plots) {
    let htmlMark = ""
    htmlMark = "<table class='gridtable'>"
    htmlMark = htmlMark +"<tr>"
    htmlMark = htmlMark +"<th>序号</th>"
    htmlMark = htmlMark +"<th>合同地块</th>"
    htmlMark = htmlMark +"<th>合同面积</th>"
    htmlMark = htmlMark +"<th>确认面积<br>（因只有名称匹配，<br>存在不准的情况）</th>"
    htmlMark = htmlMark +"<th>匹配白名单数量</th>"
    htmlMark = htmlMark +"<th>是否确认</th>"
    htmlMark = htmlMark +"<th>资源中状态</th>"
    htmlMark = htmlMark +"<th>是否白名单</th>"
    htmlMark = htmlMark +"</tr>"

    plots.map((item ,index)=> {
        let idx = index + 1;
        htmlMark = htmlMark + "<tr>"
        htmlMark = htmlMark + "<td>" + idx + "</td>"
        htmlMark = htmlMark + "<td>" + item.landNumber + "</td>"
        htmlMark = htmlMark + "<td>" + item.area + "</td>"
        htmlMark = htmlMark + "<td>" + item.insureArea + "</td>"
        htmlMark = htmlMark + "<td>" + item.whiteCount + "</td>"
        htmlMark = htmlMark + "<td>" + formatterYN(item.bind) + "</td>"
        htmlMark = htmlMark + "<td>" + formatterValid(item.resStatus) + "</td>"
        htmlMark = htmlMark + "<td>" + formatterYN(item.white) + "</td>"
        htmlMark = htmlMark + "</tr>"
    });
    htmlMark = htmlMark + "</table>"
    return htmlMark
}

/** 搜索按钮操作 */
function handleQuery() {
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
}

/** 提交按钮 */
function submitForm() {
}
function addPlotX(row){
    let orgCode = row.orgCode;
    let scInsureConfInfo = row.scInsureConfInfo;
    let yearNo = scInsureConfInfo.statYear;
    let insureConfId = scInsureConfInfo.insureConfId;
    let param = {
        "yearNo":yearNo,
        "orgCode":orgCode,
        "insureConfId":insureConfId
    }
    addPlot(param).then(response => {
        ElMessageBox.alert(response.data, '消息');
    });
}
function addPlotY(row){
    let orgCode = row.orgCode;
    let scInsureConfInfo = row.scInsureConfInfo;
    let yearNo = scInsureConfInfo.statYear;
    let insureConfId = scInsureConfInfo.insureConfId;
    let param = {
        "yearNo":yearNo,
        "orgCode":orgCode,
        "insureConfId":insureConfId,
        "remark":"通过白名单补录"
    }
    addWhitePlot(param).then(response => {
        ElMessageBox.alert(response.data, '消息');
    });
}
function syncDataX(row){
    let scInsureConfInfo = row.scInsureConfInfo;
    let insureConfId = scInsureConfInfo.insureConfId;
    let contactId = scInsureConfInfo.contactId;
    let param = {
        "contactId":contactId,
        "insureConfId":insureConfId,
        "mark":1
    }
    contactSync(param).then(response => {
        ElMessageBox.alert(response.data, '消息');
    });
}


function handleExport() {
    proxy.$refs["queryRef"].validate(valid => {
        if (valid) {
            if(queryCertNo.value!=='') {
                maskCertNo.value = btoa(queryCertNo.value)
                queryParams.value.certNo = maskCertNo.value
            }
            let url = `${window.VITE_APP_BASE_API}/bdh-samic-confirm/confirm/valid/insure/exportXls`
            proxy.download(url, {
                ...queryParams.value
            }, `确认稽核_${queryParams.value.orgCode}_${new Date().getTime()}.xlsx`)
        }});
}
const handleOrgCodeQuery = (data) => {
    queryParams.value.orgCode = data.orgCode
}


/** 获取字典 */
const getOptions = () => {
    // statYear
    getDicts("confirm_stat_year").then(response => {
        statYear.value = response.data
    })
}


getOptions()

</script>
<style type="text/css">
.gridtable {
    font-family: verdana,arial,sans-serif;
    font-size:11px;
    color:#333333;
    border-width: 1px;
    border-color: #666666;
    border-collapse: collapse;
}
.gridtable th {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #dedede;
}
.gridtable td {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #ffffff;
}
</style>
