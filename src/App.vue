<template>
  <router-view />
  <t-dialog ref="dlg"><t-dialog ref="dlg2" ><t-dialog ref="dlg3" /></t-dialog></t-dialog>
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import tDialog from "@/views/bdh-trace/components/com/dialog.vue";
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
const dlg = ref();
const dlg2 = ref();
const dlg3 = ref();
window.$d = (com, config) => {
  if (dlg.value.isShow) {
    config.append = true;
    if (dlg2.value.isShow) {
      return dlg3.value.show(com, config);
    }else{
      return dlg2.value.show(com, config);
    }
  } else {
    return dlg.value.show(com, config);
  }
};
</script>
<style lang="scss">
.dialog_class{
  width: 580px !important;
  left: 0  !important;
  right: 0  !important;
  top: 0 !important;
  bottom: 0 !important;
  margin: auto;
  .el-overlay-dialog{
    width: 580px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.dialog_unassiged{
  width: 560px !important;
  left: 0  !important;
  right: 0  !important;
  top: 0 !important;
  bottom: 0 !important;
  margin: auto;
  margin-right: 80px;
  .el-overlay-dialog{
    width: 560px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    margin-right: 0px;
  }
}
</style>
