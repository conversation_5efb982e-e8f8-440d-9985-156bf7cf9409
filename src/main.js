import { createApp } from 'vue'

import Cookies from 'js-cookie'
import $ from 'jquery';

import ElementPlus,{ ElTable, ElTableColumn } from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'
import MyIcon from '@/views/syslandresource/components/MyIcon/MyIcon.vue';
import '@/assets/styles/index.scss' // global css
import 'virtual:uno.css'
// import 'bytemd/dist/index.css' //bytemd markdown编辑器
import mitt from 'mitt'
import AMapLoader from "@amap/amap-jsapi-loader";

const $bus = {}
const emitter = mitt()
$bus.$on = emitter.on
$bus.$emit = emitter.emit
$bus.$off = emitter.off
window.$bus = $bus
window.$=$

import App from './App'
import store from './store'
import useUserStore from "@/store/modules/user"
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins
import caches from "./plugins/cache";
import { download } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'
import './views/syslandresource/assets/iconFonts/resource/iconfont.css'
import './permission' // permission control

import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/tx'



// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 归属单位
import OrgSelects from '@/components/OrgSelects'
// 懒加载
import VueLazyload from 'vue-lazyload';

import './public-path'
import { createWebHistory, createRouter } from 'vue-router'
import { constantRoutes } from './router'
import { executePermission } from './permission' // permission control
import { renderWithQiankun, qiankunWindow} from 'vite-plugin-qiankun/dist/helper'
import { ElMessage } from "element-plus";
// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

let router = null
let app = null
let history = null
ElTable.props.border = { type: Boolean, default: true } // 边框线
function render(props = {}) {
  const { container } = props;
  if (!router) {
    // basename 动态生成
    let basename = undefined;
    if(qiankunWindow.__POWERED_BY_QIANKUN__) {
      /**
       * 根据当前 url 自动提取路由前缀。
       *
       * 例如: /agric-group/projectManage/projectConfig -> /agric-group
       */
      basename = props.basename;
    }
    history = createWebHistory(basename);
    router = createRouter({
      history,
      routes: constantRoutes,
      scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
          return savedPosition;
        } else {
          return { top: 0 };
        }
      },
    });
    executePermission(router);
    window.router = router;
  }

  app = createApp(App);
  window.$$ = {
    org: caches.local.getJSON("org") || {},
    msg: {
      show: (message) => ElMessage({ message, type: "success" }),
      warn: (message) => ElMessage({ message, type: "warning" }),
      err: (message) => ElMessage({ message, type: "error" }),
    }
  };
  // 全局方法挂载
  app.config.globalProperties.download = download
  app.config.globalProperties.parseTime = parseTime
  app.config.globalProperties.resetForm = resetForm
  app.config.globalProperties.handleTree = handleTree
  app.config.globalProperties.addDateRange = addDateRange
  app.config.globalProperties.selectDictLabel = selectDictLabel
  app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.$bus = $bus
  // 全局组件挂载
  app.component('DictTag', DictTag)
  app.component('Pagination', Pagination)
  app.component('TreeSelect', TreeSelect)
  app.component('FileUpload', FileUpload)
  app.component('ImageUpload', ImageUpload)
  app.component('ImagePreview', ImagePreview)
  app.component('RightToolbar', RightToolbar)
  app.component('Editor', Editor)
  app.component("OrgSelects", OrgSelects)
  app.component("MyIcon", MyIcon)

  setupFormCreate(app)

  app.use(router);
  app.use(store);
  app.use(plugins);
  app.use(elementIcons);
  app.component("svg-icon", SvgIcon);

  directive(app);

  // 使用element-plus 并且设置全局的大小
  app.use(ElementPlus, {
    locale: locale,
    // 支持 large、default、small
    size: Cookies.get("size") || "default",
  });
  // 懒加载
  app.use(VueLazyload, {
    preLoad: 1.3,
    error: "dist/error.png",
    loading: "dist/loading.gif",
    attempt: 1,
  });
  app.mount(container ? container.querySelector("#app") : "#app");
}
//加载地图

AMapLoader.load({
  'key': 'f6ae729e24f450f6bc1d5aff6b891d61',
  'version': '2.0',   // 指定要加载的 JSAPI 的版本，缺少时默认为 1.4.15
  'plugins': [
    "AMap.ToolBar",
    "AMap.MapType",
    "AMap.Geolocation",
    "AMap.Scale",
    "AMap.ControlBar"
  ],
  'AMapUI': {
    version: "1.1",
    plugins: ["overlay/SimpleMarker", "overlay/SimpleInfoWindow"]
  }
}).then((AMap) => {
  app.use(AMap)
})


if (qiankunWindow.__POWERED_BY_QIANKUN__) {
  renderWithQiankun({
    mount(props) {
      // 从父应用中获取数据
      window.bdhMicroMainEvents = props.bdhMicroMainEvents;
      render(props);
    },
    bootstrap() {},
    async update(props) {},
    unmount(props) {
      // 卸载应用时，清理用户角色状态
      useUserStore().setRoles([])
      app.unmount();
      app._container.innerHTML = "";
      app = null;
      router = null
      debugger
    },
  })
} else {
  render({});
}

