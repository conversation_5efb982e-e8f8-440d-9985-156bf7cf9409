<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <top-nav id="topmenu-container" class="topmenu-container"  />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <header-search id="header-search" class="right-menu-item" style="padding-right: 16px;cursor: pointer"/>


          <el-dropdown @command="handleCommand" style="cursor: pointer" trigger="click">
              <div class="question-wrapper" @click="goto" >
                  <svg-icon class-name="help-document-svg" icon-class="问题反馈" style="height: 18px;width: 18px;" />
                  <span class="svg-text-span">问题反馈</span>
              </div>
              <template #dropdown>
                  <el-dropdown-menu>
                      <el-dropdown-item @click.native="showSubmitQuestionFun">
                          <span>提交工单</span>
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="showMyQuestion">
                          <span>我的工单</span>
                      </el-dropdown-item>
                  </el-dropdown-menu>
              </template>
          </el-dropdown>

          <QuestionFeedback ref="questionListRef" v-if="showQuestionFeedback" @close-my-question="closeMyQuestion"></QuestionFeedback>
          <SubmitQuestion ref="submitQuestionRef" v-if="showSubmitQuestion" @close-my-question="closeSubmitQuestionFun"></SubmitQuestion>


        <el-tooltip content="消息" effect="dark" placement="bottom">
          <Message id="message" class="right-menu-item hover-effect" style="padding-right: 8px;"/>
        </el-tooltip>
        <el-tooltip content="帮助文档" effect="dark" placement="bottom">
          <HelpDocument id="help-document" class="right-menu-item hover-effect" />
        </el-tooltip>
<!--        <screenfull id="screenfull" class="right-menu-item hover-effect" />-->

<!--        <el-tooltip content="布局大小" effect="dark" placement="bottom">-->
<!--          <size-select id="size-select" class="right-menu-item hover-effect" />-->
<!--        </el-tooltip>-->

        <el-divider direction="vertical" style="margin-top: 6px;height: 38px;"/>
      </template>
      <div class="avatar-container">

        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">

          <div class="avatar-wrapper">
            <img src="@/assets/head.png" class="user-avatar" />
              <div class="user-name">{{userStore.name}}</div>
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
<!--              <router-link to="/user/profile">-->
<!--                <el-dropdown-item>修改密码</el-dropdown-item>-->
<!--              </router-link>-->
              <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav1'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
import HelpDocument from '@/components/HelpDocument/index.vue'
import Message from '@/components/Message/index.vue'
import CustomerService from '@/components/CustomerService'

import QuestionFeedback from './QuestionFeedback/questionList'
import SubmitQuestion from './QuestionFeedback/submitQuestion'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
const { proxy } = getCurrentInstance()
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const showQuestionFeedback = ref(false)
const showSubmitQuestion = ref(false)

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index';
    })
  }).catch(() => { });
}


function showMyQuestion(){
    showQuestionFeedback.value = true
}

function closeMyQuestion(){
    showQuestionFeedback.value = false
}
function showSubmitQuestionFun(){
    console.log(1111)
    showSubmitQuestion.value = true
}
function closeSubmitQuestionFun(){
    showSubmitQuestion.value = false
}





const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}
</script>

<style lang='scss' scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

      .question-wrapper{
        padding-right: 8px;
          font-size: 14px;
          justify-content: center;align-items: center;display: flex;
          .svg-text-span{
              margin-left: 3px;
              font-weight: bold;
          }
      }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;
        color: #1e1e1e;
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .user-avatar {
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 15px;
        }
        .user-name{
          padding-left: 10px;
          color: black;
          font-size: 14px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
