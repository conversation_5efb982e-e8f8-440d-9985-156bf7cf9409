<template>
    <div class="component-upload-image">
        <el-upload
            :action="uploadImgUrl"
            list-type="picture-card"
            :on-success="handleUploadSuccess"
            :on-exceed="handleExceed"
            :before-upload="handleBeforeUpload"
            :on-error="handleUploadError"
            :on-preview="handlePictureCardPreview"
            :on-progress="handleUploadProgress"
            name="file"
            accept=".png,.jpg,.jpeg"
            :show-file-list="true"
            :limit="limit"
            :headers="headers"
            :before-remove="beforeRemove"
            style="display: inline-block; vertical-align: top;"
        >
            <el-icon slot="default" size="16px" class="base-icon">
                <Plus/>
            </el-icon>

        </el-upload>
        <!-- 上传提示 -->
        <div class="el-upload__tip">
            <template v-if="limit">
                最多可上传 <b style="color: #f56c6c">{{ limit }}</b> 张
            </template>
        </div>
        <el-dialog v-model="dialogVisible" title="预览" width="800" append-to-body>
            <img :src="currentUrl" style="display: block; max-width: 100%; margin: 0 auto;">
        </el-dialog>
    </div>
</template>

<script setup>

import {ref, watch, computed, defineComponent} from 'vue';
import {getToken} from "@/utils/auth";

const env = import.meta.env
const emit = defineEmits(['ok']);

const props = defineProps({
    modelValue: [String, Object, Array],
    // 图片数量限制
    limit: {
        type: Number,
        default: 5,
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
        type: Array,
        default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
        type: Boolean,
        default: true,
    },
})
console.log('---------getToken()')
console.log(getToken())
const pictures = ref([]);
const dialogVisible = ref(false);
const currentUrl = ref('');
const uploadImgUrl = ref(`${window.VITE_APP_BASE_API}/bdh-question-feedback-api/file/upload`) // 上传的图片服务器地址
const headers = ref({"access-token": getToken()});

const handlePictureCardPreview = (file) => {
    currentUrl.value = file.url;
    dialogVisible.value = true;
};

const removeImage = () => {
    pictures.value = [];
    emit("picturechange", pictures.value);
};

const clearPictures = () => {
    pictures.value = [];
};
const handleUploadSuccess = (res) => {
    console.log(11222222333333)
    pictures.value.push(res.data);
    emit("picturechange", pictures.value);
};

const handleExceed = () => {
    this.$message.error(`只允许上传${props.limit}个文件`);
};

const beforeRemove = (file) => {
    pictures.value = pictures.value.filter(pic => pic !== file.response.data);
    emit("picturechange", pictures.value);
};

function handleUploadProgress(event, file, fileList) {
    // 处理上传进度事件
    console.log(event); // event.percent 表示进度百分比
}

const handleBeforeUpload = (file) => {
    // 校检文件类型
    const fileType = ['png', 'jpg', 'jpeg'];
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    const isTypeOk = fileType.some((type) => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
    });
    if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.join("/")}格式文件!`)
        return false;
    }
    // 校检文件大小
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    return true;
};

const handleUploadError = () => {
    proxy.$modal.msgError("上传文件失败")
};

watch(() => props.value, (newValue) => {
    if (newValue) {
        pictures.value = [newValue];
    } else {
        pictures.value = [];
    }
});


defineExpose({
    clearPictures
});

</script>

<style scoped lang="scss">
:deep(.el-upload-list--picture-card .el-upload-list__item),
:deep(.el-upload--picture-card) {
    width: 80px!important;
    height: 80px!important;
}


.image {
    position: relative;

    .mask {
        opacity: 0;
        position: absolute;
        top: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        transition: all 0.3s;
    }

    &:hover .mask {
        opacity: 1;
    }
}
</style>
