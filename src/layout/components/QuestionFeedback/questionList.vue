<template>
    <div class="record">
        <!-- 添加或修改BomcProblemRecordController对话框 -->
        <el-dialog :title="title" :close-on-click-modal="false" v-model="open" width="1200px" append-to-body
                   @close="closeQuestion">
            <div v-show="toggle">
                <el-form :model="queryParams" ref="queryform" :inline="true" v-show="showSearch"
                         label-width="68px" label-position="left"
                >
                    <el-row :gutter="20" style="display: flex;  flex-direction: row;flex-wrap: wrap">
                        <el-col :span="6">
                            <el-form-item label="提交日期" prop="createTimeStr" style="width:100%">
                                <el-date-picker
                                    v-model="queryParams.createTimeStr"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择日期"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="工单类型" prop="workOrderType" style="width:100%">
                                <el-select
                                           v-model="queryParams.workOrderType"
                                           placeholder="请选择工单类型"
                                           clearable
                                >
                                    <el-option
                                        v-for="item in workOrderTypes"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="处理状态" prop="workOrderStatus" style="width:100%">
                                <el-select
                                           v-model="queryParams.workOrderStatus"
                                           placeholder="请选择处理状态"
                                           clearable
                                           @keyup.enter.native="handleQueryList"
                                >
                                    <el-option
                                        v-for="item in workOrderStatusList"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="系统名称" prop="systemCode" style="width:100%">
                                <el-select
                                           v-model="systemCode"
                                           placeholder="请选择系统名称"
                                           clearable
                                           @change="getSystemId"
                                >
                                    <el-option
                                        v-for="item in systemCodes"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="工单归属" prop="authId" style="width:100%">
                                <el-cascader style="width:100%"
                                             v-model="queryParams.authId"
                                             :options="systemMenus"
                                             placeholder="请选择工单归属"
                                             :render-after-expand="false"
                                             filterable
                                             clearable
                                             change-on-select
                                             :props="treeProps"
                                             @change="handleNodeClickEvent"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="18" style="text-align: right;">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleQueryList">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>

                <el-table height="470" :data="workOrderList"
                          @selection-change="handleSelectionChange" style="margin-top: 10px" border
                >
                    <el-table-column type="selection" width="55" align="center"/>
                    <el-table-column label="工单编号" align="center" prop="workOrderId" min-width="100px"/>
                    <el-table-column label="工单类型" align="center" prop="workOrderType" min-width="100px"
                                     :formatter="workOrderTypeFormat"
                    />
                    <el-table-column label="工单系统" align="center" prop="systemName" min-width="100px"/>
                    <el-table-column label="工单归属" align="center" prop="authName" min-width="100px"/>
                    <el-table-column label="工单描述" align="center" prop="workOrderDesc" min-width="100px"
                                     show-overflow-tooltip
                    />
                    <el-table-column label="处理状态" align="center" prop="workOrderStatus" min-width="100px"
                                     :formatter="workOrderStatusFormat"
                    />
                    <el-table-column label="工单级别" align="center" prop="orderPriority" min-width="100px"
                                     :formatter="workOrderLevelFormat"
                    />
                    <el-table-column label="创建人电话" align="center" prop="telephone" min-width="100px"/>
                    <el-table-column label="创建人姓名" align="center" prop="createName" min-width="100px"/>
                    <el-table-column label="创建时间" align="center" prop="createTime" min-width="160px">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="关闭时效" align="center" prop="closeDuration" min-width="120px"/>
                    <el-table-column label="操作" align="center" min-width="100px" class-name="small-padding fixed-width"
                                     fixed="right"
                    >
                        <template #default="scope">
                            <el-button link type="text" @click="handleView(scope.row)">查看</el-button>
                            <el-button v-if="!(scope.row.workOrderStatus === '04')" link type="text"
                                       @click="handleClose(scope.row)"
                            >关闭
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                    v-show="total>0"
                    :total="total"
                    :page.sync="queryParams.page"
                    :limit.sync="queryParams.rows"
                    @pagination="getList"
                />
            </div>
        </el-dialog>

        <el-dialog title="查看工单" :close-on-click-modal="false" v-model="detailOpen" width="826px" append-to-body>
            <el-form ref="questionInfoRef" :model="form" :rules="rules" label-width="90px"
                     style="margin-left: 22px;margin-right: 22px" label-position="left"
            >
                <el-row class="view_label_background" :gutter="10" type="flex" align="middle">
                    <span class="view_label_font">工单详情</span>
                </el-row>
                <el-row :gutter="5" style="display: flex; flex-wrap: wrap;margin-top: 20px">
                    <el-col :span="8">
                        <el-form-item label="工单类型:">
              <span v-for="item in workOrderTypes" class="view_font"
                    v-show="item.code === workOrderDetail.orderInfo.workOrderType"
              >{{ item.name }}
              </span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="归属系统:">
                            <span class="view_font">{{ workOrderDetail.orderInfo.systemName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="工单归属:">
                            <span class="view_font">{{ workOrderDetail.orderInfo.authName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="提交人:">
                            <span class="view_font">{{ workOrderDetail.orderInfo.createName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="提交时间:">
              <span class="view_font">{{
                      parseTime(workOrderDetail.orderInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
                  }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="组织架构:">
                            <span class="view_font">{{ workOrderDetail.orderInfo.orgName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="工单描述:">
                            <span class="view_font">{{ workOrderDetail.orderInfo.workOrderDesc }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="图片或视频:">

                            <template v-for="item in workOrderDetail.orderInfo.pictures">
                                <el-image
                                    style="width: 80px; height: 80px;margin-right: 8px;margin-bottom: 8px;border-radius: 5%;cursor: pointer;"
                                    :src="item"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :initial-index="0"
                                    @click="previewPic(item)"
                                    fit="cover"
                                />
                            </template>
                            <template v-for="item in workOrderDetail.orderInfo.videos">
                                <!--                <video width="80px" height="80px" controls autoplay :src="item" />-->
                                <video class="view_video" :src="item" @click="previewVideo(item)"
                                       style="margin-bottom: 8px;"/>
                            </template>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="附件:">

                            <template v-for="item in workOrderDetail.orderInfo.files">
                                <el-row>
                                    <el-link :underline="false" @click="download(item)">
                                        <span class="el-icon-document"> {{
                                                item.substring(item.indexOf("?name=") + 6)
                                            }} </span>
                                    </el-link>
                                </el-row>
                            </template>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="view_label_background" :gutter="10" type="flex" align="middle">
                    <span class="view_label_font">反馈情况</span>
                </el-row>
                <template v-if="workOrderDetail.priorityList" v-for="item in workOrderDetail.priorityList">
                    <el-row :gutter="2"
                            style="display: flex; flex-wrap: wrap;margin-top: 10px;
                        border: 1px solid lightgray;border-radius: 4px;"
                    >
                        <el-col :span="8" style="margin-top: 15px;">
                            <el-row>
                                <el-col :span="1" style="margin-top: 8px">
                                    <div
                                        style="width: 3px;height: 15px;border-radius: 0px 2px 2px 0px;background-color: #326DFF;margin-left: -1px"
                                    >
                                    </div>
                                </el-col>
                                <el-col :span="23">
                                    <el-form-item label-width="80px" label="确认人:">
                      <span class="view_font">{{
                              item.createName
                          }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                        </el-col>

                        <el-col :span="14" style="margin-top: 15px;">
                            <el-form-item label="确认时间:">
                      <span class="view_font">{{
                              parseTime(item.createTime)
                          }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="2">
                            <img
                                v-if="item.orderPriority === 3"
                                src="./assets/priority-high.png"
                                style="width: 36px; height: 39px; margin-right: 20px"
                            />
                            <img
                                v-if="item.orderPriority === 2"
                                src="./assets/priority-middle.png"
                                style="width: 36px; height: 39px; margin-right: 20px"
                            />
                            <img
                                v-if="item.orderPriority == 1"
                                src="./assets/priority-bad.png"
                                style="width: 36px; height: 39px; margin-right: 20px"
                            />
                        </el-col>
                        <el-col :span="1">
                        </el-col>
                        <el-col :span="22">
                            <div
                                style="border-radius: 4px;height: 42px;background-color: rgba(50, 109, 255, 0.05);margin-bottom: 15px"
                            >
                                <div style="line-height: 42px">
                          <span
                              style="width: 68px;height: 26px;border-radius: 4px;padding: 2px 6px 2px 6px;background-color: rgba(50, 109, 255, 0.15);margin-left: 10px"
                          >
                        <span style="color:rgba(50, 109, 255, 1) ">温馨提示</span>
                      </span>
                                    {{ item.priorityDesc }}
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="1">
                        </el-col>
                    </el-row>


                </template>

                <el-row :gutter="2"
                        style="display: flex; flex-wrap: wrap;margin-top: 10px;
                        border: 1px solid lightgray;border-radius: 4px;"
                        v-if="workOrderDetail.editable && workOrderDetail.priorityList.length===0 && !workOrderDetail.replyInfo.replyId && !workOrderDetail.supportInfo.supportId"
                >
                    <el-col :span="1">
                    </el-col>
                    <el-col :span="22">
                        <div
                            style="border-radius: 4px;height: 42px;background-color: rgba(50, 109, 255, 0.05);margin-bottom: 15px;margin-top: 15px"
                        >
                            <div style="line-height: 42px">
                          <span
                              style="width: 68px;height: 26px;border-radius: 4px;padding: 2px 6px 2px 6px;background-color: rgba(50, 109, 255, 0.15);margin-left: 10px"
                          >
                        <span style="color:rgba(50, 109, 255, 1) ">温馨提示</span>
                      </span>
                                请耐心等待，工作人员将会尽快回复！
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="1">
                    </el-col>
                </el-row>

                <el-row :gutter="2"
                        style="display: flex; flex-wrap: wrap;margin-top: 10px;
                        border: 1px solid lightgray;border-radius: 4px;"
                        v-if="!workOrderDetail.editable && workOrderDetail.priorityList.length===0 && !workOrderDetail.replyInfo.replyId && !workOrderDetail.supportInfo.supportId"
                >
                    <el-col :span="1">
                    </el-col>
                    <el-col :span="22">
                        <div
                            style="border-radius: 4px;height: 42px;background-color: rgba(50, 109, 255, 0.05);margin-bottom: 15px;margin-top: 15px"
                        >
                            <div style="line-height: 42px">
                          <span
                              style="width: 68px;height: 26px;border-radius: 4px;padding: 2px 6px 2px 6px;background-color: rgba(50, 109, 255, 0.15);margin-left: 10px"
                          >
                        <span style="color:rgba(50, 109, 255, 1) ">温馨提示</span>
                      </span>
                                工单已被用户关闭！
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="1">
                    </el-col>
                </el-row>
                <template v-if="workOrderDetail.supportInfo.supportId">
                    <el-row :gutter="2"
                            style="display: flex; flex-wrap: wrap;margin-top: 20px;
                        border: 1px solid lightgray;border-radius: 4px;">
                        <el-col :span="8" style="margin-top: 15px;">
                            <el-row>
                                <el-col :span="1" style="margin-top: 8px">
                                    <div
                                        style="width: 3px;height: 15px;border-radius: 0px 2px 2px 0px;background-color: #326DFF;margin-left: -1px"
                                    >
                                    </div>
                                </el-col>
                                <el-col :span="23">
                                    <el-form-item label-width="80px" label="技术支持:">
                      <span class="view_font">
                          工单已转移至信息公司
                        </span>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                        </el-col>
                        <el-col :span="8" style="margin-top: 15px;">
                            <el-form-item label-width="80px" label="确认人:">
                      <span class="view_font">{{
                              workOrderDetail.supportInfo.supportNm
                          }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" style="margin-top: 15px;">
                            <el-form-item label="确认时间:">
                      <span class="view_font">{{
                              parseTime(workOrderDetail.supportInfo.supportTime, '{y}-{m}-{d} {h}:{i}:{s}')
                          }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </template>

                <template v-if="workOrderDetail.replyInfo && workOrderDetail.replyInfo.replyId">
                    <el-row>
                        <el-col :span="24" style="margin-top: 15px;">
                            <el-form-item label="工单回复:"> {{ workOrderDetail.replyInfo.replyContent }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item>
                                <template v-for="item in workOrderDetail.replyInfo.pictures">
                                    <el-image
                                        style="width: 80px; height: 80px;margin-right: 8px;border-radius: 5%;cursor: pointer;"
                                        :src="item"
                                        :zoom-rate="1.2"
                                        :max-scale="7"
                                        :min-scale="0.2"
                                        @click="previewPic(item)"
                                        :initial-index="4"
                                        fit="cover"
                                    />
                                </template>
                                <template v-for="item in workOrderDetail.replyInfo.videos">
                                    <video class="view_video" :src="item" @click="previewVideo(item)"/>
                                </template>
                            </el-form-item>

                        </el-col>
                        <!--          <el-col :span="24">-->
                        <!--            <el-form-item label-width="90px" label="添加知识库:">-->
                        <!--              是-->
                        <!--            </el-form-item>-->

                        <!--          </el-col>-->
                    </el-row>
                </template>


                <el-row v-if="workOrderDetail.editable" style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="用户评价:">
                            <el-image v-for="(item,index) in starArr" :src="item.src"
                                      style="width: 22px;height: 22px;margin-right: 5px;margin-top: 5px"
                                      @click="checkStar(item.type,index)"
                            ></el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" style="margin-top: -15px">
                        <!--            <el-form-item>-->
                        <el-input v-model="evaluationContent" type="textarea" :rows="2"
                                  placeholder="请留下您的宝贵建议，您的建议是我们前进的动力，感谢配合！"
                        ></el-input>
                        <!--            </el-form-item>-->
                    </el-col>
                </el-row>
                <el-row v-else style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="用户评价:">
                            <img v-for="i in workOrderDetail.evaluationInfo.evaluationLevel"
                                 src="./assets/icon_star_light.png"
                                 style="width: 22px;height: 22px;margin-right: 5px;margin-top: 5px"
                            ></img>
                            <img v-for="j in (5 - workOrderDetail.evaluationInfo.evaluationLevel)"
                                 src="./assets/icon_star_dark.png"
                                 style="width: 22px;height: 22px;margin-right: 5px"
                            ></img>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24" style="margin-top: -15px">
                        <el-input type="textarea" :disabled="true"
                                  v-model="workOrderDetail.evaluationInfo.evaluationContent"
                                  :rows="3" placeholder="请留下您的宝贵建议，您的建议是我们前进的动力，感谢配合！"
                        ></el-input>
                    </el-col>
                </el-row>
                <div class="dialog-footer" style="text-align: right;margin-top: 10px"
                     v-if="workOrderDetail.editable"
                >
                    <el-button type="primary" @click="closeView">关闭工单</el-button>
                </div>
            </el-form>
        </el-dialog>
        <el-dialog v-model="showPreviewVideo" v-if="showPreviewVideo" append-to-body>
            <video style="width: 100%;height: 400px;" autoplay muted preload controls="controls">
                <source v-bind:src="previewVideoUrl" type="video/mp4">
            </video>
        </el-dialog>
        <el-dialog v-model="showPreviewPic" v-if="showPreviewPic" append-to-body>
            <img style="width: 100%;height: 400px;" :src="previewPicUrl"/>

        </el-dialog>
    </div>
</template>


<script setup name="questionList">
import {
    closeWorkOrder,
    findAuthByMenu,
    findSystemByCode,
    findSystemById,
    getOrderInfo,
    listOrderInfo
} from '@/layout/components/QuestionFeedback/api/orderInfo'
import darkStar from '@/layout/components/QuestionFeedback/assets/icon_star_dark.png'
import lightStar from '@/layout/components/QuestionFeedback/assets/icon_star_light.png'
import {getDicts} from '@/layout/components/QuestionFeedback/api/dict/data'
import axios from "axios";
import {ref, getCurrentInstance, computed} from 'vue';
import usePermissionStore from '@/store/modules/permission'
import {parseTime} from "../../../utils/tx.js";


const {proxy} = getCurrentInstance();

const env = import.meta.env;
const emit = defineEmits(['ok']);

const props = defineProps({
    darkStar: {
        type: String,
        default: darkStar,
    },
    lightStar: {
        type: String,
        default: lightStar,
    }
})

// 所有的路由信息
const permissionStore = usePermissionStore()
const systemCodes = computed(() => {
    return permissionStore.topbarRouters.map((item) => {
        return {
            code: item.name,
            name: item.meta.title
        }
    })
})
const dialogImageUrl = ref('');
const loading = ref(true);
const imageDialogVisible = ref(false);
const toggle = ref(true);
const ids = ref([]);
const names = ref([]);
const fileUpload = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref('我的工单');
const open = ref(true);
const detailOpen = ref(false);
const queryParams = ref({
    page: 1,
    rows: 10,
    createTimeStr: null,
    workOrderType: null,
    workOrderStatus: null,
    systemId: null,
    authId: null
});
const form = ref({});
const workOrderList = ref([]);
const workOrderTypes = ref([]);
const workOrderStatusList = ref([]);

const systemCode = ref(null);
const systemId = ref(null);
const systemMenus = ref([]);
const workOrderDetail = ref({
    'editable': null,
    'orderInfo': {
        'orgCode': null,
        'orgName': null,
        'workOrderId': null,
        'workOrderType': null,
        'workOrderDesc': null,
        'systemId': null,
        'authId': null,
        'authName': null,
        'workOrderStatus': null,
        'isSupport': null,
        'createBy': null,
        'createName': null,
        'createTime': null,
        'closeTime': null,
        'supportTime': null,
        'supportPerson': null,
        'supportPersonNm': null,
        'systemName': null
    },
    'priorityList': [],
    'supportInfo': {
        'supportTime': null,
        'supportId': null,
        'supportNm': null
    },
    'replyInfo': {
        replyId: null
    },
    evaluationInfo: {
        evaluationLevel: null,
        evaluationContent: null,
    }
});
const showPreviewPic = ref(false);
const previewPicUrl = ref('');
const showPreviewVideo = ref(false);
const previewVideoUrl = ref(null);
const evaluationLevel = ref(null);
const evaluationContent = ref(null);
const starArr = ref([
    {type: 0, src: props.darkStar},
    {type: 0, src: props.darkStar},
    {type: 0, src: props.darkStar},
    {type: 0, src: props.darkStar},
    {type: 0, src: props.darkStar}
]);
const orderLevel = ref([
    {name: "一般", code: '1'},
    {name: "中级", code: '2'},
    {name: "高级", code: '3'}
]);
const rules = ref({
    problemType: [{required: true, message: '必填项', trigger: 'change'}]
});
const treeProps = ref({
    value: 'authId',
    label: 'name',
    children: 'children'
});

function closeQuestion() {
    emit('closeMyQuestion', true)
}

function closeVideo() {
    previewVideoUrl.value = null;
}

function closeViewDialog() {
    workOrderDetail.value = {
        'editable': null,
        'orderInfo': {
            'orgCode': null,
            'orgName': null,
            'workOrderId': null,
            'workOrderType': null,
            'workOrderDesc': null,
            'systemId': null,
            'authId': null,
            'authName': null,
            'workOrderStatus': null,
            'isSupport': null,
            'createBy': null,
            'createName': null,
            'createTime': null,
            'closeTime': null,
            'supportTime': null,
            'supportPerson': null,
            'supportPersonNm': null,
            'systemName': null
        },
        'priorityList': [],
        'supportInfo': {
            'supportTime': null,
            'supportId': null,
            'supportNm': null
        },
        'replyInfo': {
            replyId: null
        },
        evaluationInfo: {
            evaluationLevel: null,
            evaluationContent: null,
        }
    };
    evaluationLevel.value = null;
    evaluationContent.value = null;
    starArr.value = [
        {type: 0, src: props.darkStar},
        {type: 0, src: props.darkStar},
        {type: 0, src: props.darkStar},
        {type: 0, src: props.darkStar},
        {type: 0, src: props.darkStar}
    ];
}

function previewVideo(url) {
    console.log(url)
    showPreviewVideo.value = true;
    previewVideoUrl.value = url;
}

function previewPic(url) {
    showPreviewPic.value = true;
    previewPicUrl.value = url;
}

function getList() {
    loading.value = true;
    listOrderInfo(queryParams.value).then(response => {
        if (response && response.data) {
            workOrderList.value = response.data.records;
            total.value = response.data.total;
        } else {
            console.warn('Unexpected response format:', response);
        }
        loading.value = false;
    }).catch(error => {
        console.error('Error fetching data:', error);
        loading.value = false;
    });
}

function cancel() {
    toggle.value = true;
    title.value = '问题记录统计';
    resetQueryFun();
}

function handleQueryList() {
    getList();
}

function resetQueryFun() {
    queryParams.value.createTimeStr = null;
    queryParams.value.workOrderType = null;
    queryParams.value.workOrderStatus = null;
    queryParams.value.authId = null;
    queryParams.value.page = 1;
    handleQueryList();
}

function handleSelectionChange(selection) {
    // 处理选中数据
}

function workOrderTypeFormat(row, column) {
    let workOrderType = null;
    if (workOrderTypes.value.length > 0) {
        workOrderTypes.value.forEach((v, i) => {
            if (v.code === row.workOrderType) {
                workOrderType = v.name;
            }
        });
    }
    return workOrderType;
}

function workOrderStatusFormat(row, column) {
    let workOrderStatus = null;
    if (workOrderStatusList.value.length > 0) {
        workOrderStatusList.value.forEach((v, i) => {
            if (v.code === row.workOrderStatus) {
                workOrderStatus = v.name;
            }
        });
    }
    return workOrderStatus;
}

function workOrderLevelFormat(row, column) {
    let orderPriority = null;
    if (orderLevel.value.length > 0) {
        orderLevel.value.forEach((v, i) => {
            if (v.code === row.orderPriority) {
                orderPriority = v.name;
            }
        });
    }
    return orderPriority;
}

function getSystemId() {
    if (systemCode.value) {
        findSystemByCode({systemCode: systemCode.value}).then(resp => {
            systemId.value = resp.data.systemId;
            getSystemMenu();
        });
    }
}

function getSystemById(systemId) {
    findSystemById({systemId: systemId}).then(resp => {
        workOrderDetail.value.orderInfo.systemName = resp.data.systemName;
    });
}

function download(fileUrl) {
    axios.get(fileUrl, {responseType: 'blob'})
        .then(response => {
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', fileUrl.substring(fileUrl.indexOf("?name=") + 6));
            document.body.appendChild(link);
            link.click();
        });
}

function getSystemMenu() {
    findAuthByMenu({systemId: systemId.value}).then(resp => {
        systemMenus.value = resp.data;
    });
}

function handleNodeClickEvent(a) {
    queryParams.value.authId = a[a.length - 1];
}

function handleView(row) {
    closeViewDialog();
    getOrderInfo(row.workOrderId).then(resp => {
        workOrderDetail.value = resp.data;
        getSystemById(workOrderDetail.value.orderInfo.systemId);
        detailOpen.value = true;
    });
}

function closeView() {
    proxy.$modal.confirm('确认是否关闭问题，关闭默认问题已解决!').then(function () {
        return closeWorkOrder({
            workOrderId: workOrderDetail.value.orderInfo.workOrderId,
            evaluationLevel: evaluationLevel.value,
            evaluationContent: evaluationContent.value
        });
    }).then((resp) => {
        proxy.$modal.msgSuccess(resp.msg);
        closeViewDialog();
        detailOpen.value = false;
        getList();
    }).catch(() => {
    });
}

function handleClose(row) {
    proxy.$modal.confirm('确认是否关闭问题，关闭默认问题已解决!').then(function () {
        return closeWorkOrder({workOrderId: row.workOrderId});
    }).then((resp) => {
        getList();
        proxy.$modal.msgSuccess(resp.msg);
    }).catch(() => {
    });
}

function checkStar(type, index) {
    for (let i = 0; i <= index; i++) {
        starArr.value[i].src = props.lightStar;
    }
    for (let i = index + 1; i < 5; i++) {
        starArr.value[i].src = props.darkStar;
    }
    evaluationLevel.value = index + 1;
}

/** 获取字典 */
const getOptions = () => {

    getDicts('work_order_type').then(response => {
        workOrderTypes.value = response.data
    })
    getDicts('work_order_status').then(response => {
        workOrderStatusList.value = response.data
    })
    getList()
    getSystemId()
}


getOptions();

// 使用 defineExpose 暴露方法
defineExpose({
    resetQueryFun, open
});
</script>

<style>
.record {
    position: absolute;
    z-index: 9999;
}

.view_font {
    color: #4E5969;
    font-size: 14px;
}

.view_label_font {
    font-size: 14px;
    font-weight: 700;
    margin-left: 15px;
}

.view_label_background {
    border-radius: 4px;
    height: 42px;
    background-color: #F3F5F9;
}

.view_video {
    cursor: pointer;
    width: 80px;
    height: 80px;
    margin-right: 8px
}

.el-form--inline .el-form-item {
    margin-right: 0 !important;
}

</style>
