<template>
    <div class="component-upload-image">
        <el-upload
            :action="uploadImgUrl"
            list-type="picture-card"
            :on-success="handleUploadSuccess"
            :on-exceed="handleExceed"
            :before-upload="handleBeforeUpload"
            :on-error="handleUploadError"
            :on-preview="handlePictureCardPreview"
            name="file"
            accept=".mp4,.flv"
            :show-file-list="true"
            :limit="limit"
            :headers="headers"
            :before-remove="beforeRemove" style="display: inline-block; vertical-align: top"
        >
            <el-icon slot="default" size="14px" class="base-icon">
                <Plus/>
            </el-icon>
        </el-upload>
        <!-- 上传提示 -->
        <div class="el-upload__tip">
            <template v-if="limit">
                最多可上传 <b style="color: #f56c6c">{{ limit }}</b> 个视频
            </template>
        </div>
        <el-dialog v-model="dialogVisible" append-to-body>
            <video style="width: 100%;height: 400px;" autoplay muted preload controls="controls">
                <source :src="currentVideoUrl.url" type="video/mp4">
            </video>
        </el-dialog>
    </div>
</template>

<script setup>
import {ref, watch} from 'vue';
import {getToken} from "@/utils/auth";

const env = import.meta.env
const emit = defineEmits(['ok']);
const props = defineProps({
    // 图片数量限制
    limit: {
        type: Number,
        default: 5,
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 100,
    },
})
const videos = ref([]);
const currentVideoUrl = ref({
    url: null,
});
const dialogVisible = ref(false);
const uploadImgUrl = `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/file/uploadVideo`; // 上传的图片服务器地址
const headers = ref({
    "access-token": getToken(),
});

const handlePictureCardPreview = () => {
    dialogVisible.value = true;
};

const removeImage = () => {
    emit("videochange", "");
};

const clearVideos = () => {
    videos.value = [];
};
const handleUploadSuccess = (res, file) => {
    file.url = res.data.fisrtImgPath;
    currentVideoUrl.value.url = res.data.videoPath;
    videos.value.push(res.data.videoPath);
    emit("videochange", videos.value);
};

const handleExceed = () => {
    this.$message.error(`只允许上传${props.limit}个视频`);
};

const beforeRemove = (file) => {
    videos.value = videos.value.filter(video => video !== file.response.data.videoPath);
    emit("videochange", videos.value);
};

const handleBeforeUpload = (file) => {
    // 校检文件类型
    const fileType = ['mp4', 'flv'];
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    const isTypeOk = fileType.some((type) => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
    });
    if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.join("/")}格式文件!`)
        return false;
    }
    // 校检文件大小
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    return true;
};

const handleUploadError = () => {
    this.$message({
        type: "error",
        message: "上传失败",
    });
};

watch(() => props.value, (newValue) => {
    if (newValue) {
        videos.value = [newValue];
    } else {
        videos.value = [];
    }
});

defineExpose({
    clearVideos
});

</script>

<style scoped lang="scss">

:deep(.el-upload-list--picture-card .el-upload-list__item),
:deep(.el-upload--picture-card) {
    width: 80px!important;
    height: 80px!important;
}
.image {
    position: relative;

    .mask {
        opacity: 0;
        position: absolute;
        top: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        transition: all 0.3s;
    }

    &:hover .mask {
        opacity: 1;
    }
}
</style>
