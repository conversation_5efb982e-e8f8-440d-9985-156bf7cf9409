
<template>
    <div class="record">
        <el-dialog :title="title" :close-on-click-modal="false" v-model="open" width="770px" append-to-body
                   :destroy-on-close="true" @close="cancel"
        >
            <el-form ref="questionInfoRef" :model="form" :rules="rules" label-width="120px"
                     label-position="top"
            >
                <img src="./assets/head.png" style="width: 100%;height: 80px;"/>
                <el-row :gutter="10" style="display: flex; flex-wrap: wrap; margin-top: 10px">
                    <el-col :span="6">
                        <el-form-item label="系统名称" prop="systemCode">
                            <el-select style="width:220px"
                                       v-model="systemCode"
                                       placeholder="请选择系统名称"
                                       clearable
                                       @change="getOrgAndSystem"
                            >
                                <el-option
                                    v-for="item in systemCodes"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="账号归属组织机构" prop="orgCode">
                            <el-cascader
                                v-model="defaultOrgCode"
                                :options="orgList"
                                :props="{
                  label: 'orgName',
                  value: 'orgCode',
                  expandTrigger: 'hover'
                }"
                                change-on-select
                                filterable
                                @change="handleOrgChange"
                                style="width: 100%"
                            >
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="工单类型" prop="workOrderType">
                            <el-select
                                v-model="form.workOrderType"
                                placeholder="请选择工单类型"
                                clearable
                            >
                                <el-option
                                    v-for="item in workOrderTypes"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="工单归属" prop="authId">
                            <el-cascader
                                :options="systemMenus"
                                v-model="form.authId"
                                :props="{
                  label: 'name',
                  value: 'authId',
                  expandTrigger: 'hover',
                }"
                                change-on-select
                                @change="handleAuthChange"
                                filterable
                            >
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="工单描述" prop="workOrderDesc">
                            <el-input type="textarea" :rows="2" v-model="form.workOrderDesc"
                                      placeholder="请留下您的宝贵建议，您的建议是我们前进的动力，感谢配合！"
                                      maxlength="1000"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="上传图片" prop="imgUrl">
                            <upload-img ref="pictureUpload" :limit="9" @picturechange="picturechange"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="上传视频" prop="videoUrl">
                            <video-upload ref="videoUpload" :limit="1" @videochange="videochange"></video-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="上传附件" prop="fileUrl">
                            <file-upload class="file-uploader" ref="filesUpload" :limit="9" @input="filechange"></file-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="dialog-footer" style="text-align: right">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup>
import {ref, onMounted, watch, getCurrentInstance, computed} from 'vue';
import { addOrderInfo, findAuthByMenu, findSystemByCode } from '@/layout/components/QuestionFeedback/api/orderInfo';
import { getDicts } from '@/layout/components/QuestionFeedback/api/dict/data';
import headImg from '@/layout/components/QuestionFeedback/assets/head.png';
import UploadImg from '@/layout/components/QuestionFeedback/ImageUpload';
import VideoUpload from '@/layout/components/QuestionFeedback/VideoUpload';
import FileUpload from '@/layout/components/QuestionFeedback/FileUpload';
import { queryOrgTreeByUserOrg } from '@/layout/components/QuestionFeedback/api/orgSelect';
import usePermissionStore from '@/store/modules/permission'
const env = import.meta.env;
const { proxy } = getCurrentInstance();
const emit = defineEmits(['ok']);
const props = defineProps({
    headImg: {
        type: String,
        default: headImg,
    }
});

const defaultOrgCode = ref([]);
const fileUrls = ref([]);
const dialogImageUrl = ref('');
const loading = ref(true);
const imageDialogVisible = ref(false);
const toggle = ref(true);
const ids = ref([]);
const names = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const workOrderList = ref([]);
const title = ref('提交工单');
const open = ref(true);
const queryParams = ref({
    page: 1,
    rows: 10,
    createTimeStr: null,
    workOrderType: null,
    workOrderStatus: null,
    systemId: null,
    authId: null,
    orgCode: null,
    orgName: null
});
const form = ref({
    orgCode: null,
    orgName: null,
    imgUrls: [],
    videoUrls: [],
    workOrderStatus: '01',
    videos: [],
    pictures: [],
    files: [],
    workOrderType: null
});
const workOrderTypes = ref([]);
const workOrderStatusList = ref([]);
const systemId = ref(null);
const systemMenus = ref([]);
const systemCode = ref("");
const rules = ref({
   // orgCode: [{ required: true, message: '请选择所在组织机构', trigger: 'change' }],
    workOrderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
    authId: [{ required: true, message: '请选择工单归属模块', trigger: 'change' }],
    workOrderDesc: [{ required: true, message: '请填写工单描述', trigger: 'change' }]
});
const treeProps = ref({
    value: 'authId',
    label: 'name',
    children: 'children'
});
const orgList = ref([]);

const handleOrgChange = (val) => {
    form.value.orgCode = val[val.length - 1];
};

const handleAuthChange = (val) => {
    if (val) {
        form.value.authId = val[val.length - 1];
    }
};

//账号归属组织机构
const getUserOrgList = (apiUrl) => {
  orgList.value = [];
    queryOrgTreeByUserOrg(apiUrl).then(resp => {
        orgList.value = resp.data;
        if (orgList.value.length > 0) {
            defaultOrgCode.value = [orgList.value[0].orgCode];
            handleOrgChange([orgList.value[0].orgCode]);
        }
    });
};

const picturechange = (value) => {
    console.log('-----picturechange------',value)
    form.value.pictures = value;
};

const videochange = (value) => {
    form.value.videos = value;
};

const filechange = (value) => {
    fileUrls.value = value;
};

const cancel = () => {
    emit('closeMyQuestion',true)
};

const handleSelectionChange = (selection) => {
    // 处理选中数据
};

const getOrgAndSystem = (e) => {
    getUserOrgList(e+'-api');
    getSystemId(e)
};
const getSystemId = (e) => {
    console.log(e)
    findSystemByCode({ systemCode: e }).then(resp => {
        systemId.value = resp.data.systemId;
        getSystemMenu();
        form.value.systemId = resp.data.systemId;
    });
};

const getSystemMenu = () => {
    findAuthByMenu({ systemId: systemId.value }).then(resp => {
        systemMenus.value = resp.data;
    });
};

const handleNodeClickEvent = (a) => {
    form.value.authName = a.name;
};

const dialogOpen = () => {
    open.value = true;
    toggle.value = true;
};

const submitForm = () => {
    proxy.$refs["questionInfoRef"].validate(valid => {
        if (valid) {
            form.value.videoUrls.forEach(v => form.value.videos.push(v.videoUrl));
            form.value.imgUrls.forEach(v => form.value.pictures.push(v.url));
            fileUrls.value.forEach(v => form.value.files.push(v.response.data + "?name=" + v.name));
            addOrderInfo(form.value).then(resp => {
                proxy.$refs["pictureUpload"].clearPictures();
                proxy.$refs["videoUpload"].clearVideos();
                proxy.$refs["filesUpload"].clearFiles();
                form.value.files = [];
                proxy.$modal.msgSuccess("提交成功");
                cancel();
            });
        }
    });
};

const resetForm = (formName) => {
    proxy.$refs["questionInfoRef"].resetFields();
};

watch(open, (val) => {
    if (!val) {
        form.value.pictures = [];
        form.value.videos = [];
        form.value.imgUrls = [];
        form.value.videoUrls = [];
        form.value.files = [];
        fileUrls.value = [];
    }
});


// 所有的路由信息
const permissionStore = usePermissionStore()
const systemCodes = computed(() => {
    return permissionStore.topbarRouters.map((item) => {
        return {
            code: item.name,
            name: item.meta.title
        }
    })
})

onMounted(() => {
});
/** 获取字典 */
const getOptions = () => {

    // getUserOrgList();
    // getSystemId();
    getDicts('work_order_type').then(response => {
        workOrderTypes.value = response.data;
    });
    getDicts('work_order_status').then(response => {
        workOrderStatusList.value = response.data;
    });
}
getOptions()

</script>

<style lang="scss" scoped>
.record {
    position: absolute;
    z-index: 99999;
}

:deep(.el-dialog__body) {
    padding-top: 5px !important;
}

:deep(.el-upload--picture-card) {
    width: 90px;
    height: 90px;
}

:deep(.el-upload-list__item){
    width: 90px !important;
    height: 90px !important;
}

:deep(.el-icon-plus) {
    display: flow;
    margin-top: 30px;
}

:deep(.ele-upload-list__item-content) {
    width: 100% !important;
    height: auto !important;
}
</style>
