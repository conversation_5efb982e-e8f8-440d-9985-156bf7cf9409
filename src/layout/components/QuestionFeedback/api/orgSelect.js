import request from '@/utils/request'
const env = import.meta.env
const prefix = window.VITE_APP_BASE_API


// 根据token获取用户org列表，返回组织机构树 apiUrl 各项目api前缀
export function queryOrgTreeByUserOrg (apiUrl) {
  if(apiUrl === 'sysanimalhusbandry-api') {
    apiUrl = 'bdh-animal-husbandry-api'
  } else if(apiUrl === 'systemlandcontract-api') {
    apiUrl = `${import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT}`
  }
  return request({
    url: `${window.VITE_APP_BASE_API}/${apiUrl}/org/amporg/queryOrgTreeByUserOrg`,
    method: 'post'
  })
}
