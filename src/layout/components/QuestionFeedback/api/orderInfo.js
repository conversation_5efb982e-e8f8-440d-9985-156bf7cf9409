import request from '@/utils/request'
//const env = process.env
// 查询orderInfo列表
export function findAuthByMenu(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/amp/ampAuth/findAuthByMenu`,
    method: 'post',
    data
  })
}
export function findSystemByCode(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/amp/ampAuth/findSystemByCode`,
    method: 'post',
    data
  })
}
export function findSystemById(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/amp/ampAuth/findSystemById`,
    method: 'post',
    data
  })
}
// 查询orderInfo列表
export function listOrderInfo(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/queryByPage`,
    method: 'post',
    data
  })
}

// 查询orderInfo详细
export function getOrderInfo(workOrderId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/info/` + workOrderId,
    method: 'post'
  })
}

// 新增orderInfo
export function addOrderInfo(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/insert`,
    method: 'post',
    data
  })
}

// 修改orderInfo
export function updateOrderInfo(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/update`,
    method: 'post',
    data
  })
}

// 删除orderInfo
export function delOrderInfo(workOrderId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/logicDeleteById/` + workOrderId,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delOrderInfos(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/logicDeleteByIds`,
    method: 'post',
    data
  })
}

export function closeWorkOrder(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-question-feedback-api/order/user/closeWorkOrder`,
    method: 'post',
    data
  })
}
