<template>
    <div class="upload-file">
        <el-upload
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :file-list="fileList"
            :limit="9"
            :on-error="handleUploadError"
            :on-exceed="handleExceed"
            :on-success="handleUploadSuccess"
            :show-file-list="false"
            :headers="headers"
            class="upload-file-uploader"
            ref="upload"
            accept=".doc,.xls,.ppt,.txt,.pdf,.zip"
        >
            <!-- 上传按钮 -->
            <el-button size="small" icon='Upload' type="primary">选择文件</el-button>
            <!-- 上传提示 -->
            <div class="el-upload__tip" slot="tip" v-if="showTip">
                请上传
                <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB，</b></template>
                <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}，</b></template>
                <template v-if="fileLimit"> 最多上传 <b style="color: #f56c6c">{{ fileLimit }}个</b></template>
                的文件
            </div>
        </el-upload>

        <!-- 文件列表 -->
        <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear"
                          tag="ul">
            <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content"
                v-for="(file, index) in fileList">
                <el-link :underline="false" target="_blank" @click="download(file.response.data,file.name)">
                    <span class="el-icon-document"> {{ file.name }} </span>
                </el-link>
                <div class="ele-upload-list__item-content-action">
                    <el-link :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
                </div>
            </li>
        </transition-group>
    </div>
</template>

<script setup>
    import {defineComponent, ref, reactive, computed, onMounted, getCurrentInstance} from 'vue';
    import {getToken} from "@/utils/auth";
    import axios from "axios";

    const env = import.meta.env

    const {proxy} = getCurrentInstance();
    const emit = defineEmits(['ok']);
    const props = defineProps({
        // 值
        value: [String, Object, Array],
        // 大小限制(MB)
        fileSize: {
            type: Number,
            default: 100,
        },
        // 文件类型, 例如['png', 'jpg', 'jpeg']
        fileType: {
            type: Array,
            default: () => ["doc", "xls", "ppt", "txt", "pdf", "zip"],
        },
        // 文件类型, 例如['png', 'jpg', 'jpeg']
        fileLimit: {
            type: Number,
            default: 9,
        },
        // 是否显示提示
        isShowTip: {
            type: Boolean,
            default: true
        }
    })

    const uploadFileUrl = ref(`${window.VITE_APP_BASE_API}/bdh-question-feedback-api/file/upload`);
    const headers = reactive({
      "access-token": getToken(),
    });
    const fileList = ref([]);

    const showTip = computed(() => {
        return props.isShowTip && (props.fileType || props.fileSize);
    });

    const list = computed(() => {
        let temp = 1;
        if (props.value) {
            // 首先将值转为数组
            const list = Array.isArray(props.value) ? props.value : [props.value];
            // 然后将数组转为对象数组
            return list.map((item) => {
                if (typeof item === "string") {
                    item = {name: item, url: item};
                }
                item.uid = item.uid || new Date().getTime() + temp++;
                return item;
            });
        } else {
            fileList.value = [];
            return [];
        }
    });

    const clearFiles = () => {
        fileList.value = [];
    };

    const download = (fileUrl, fileName) => {
        axios.get(fileUrl, {responseType: 'blob'})
            .then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', fileName);
                document.body.appendChild(link);
                link.click();
            });
    };

    const handleBeforeUpload = (file) => {
        // 校检文件类型
        if (props.fileType) {
            let fileExtension = "";
            if (file.name.lastIndexOf(".") > -1) {
                fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
            }
            const isTypeOk = props.fileType.some((type) => {
                if (file.type.indexOf(type) > -1) return true;
                if (fileExtension && fileExtension.indexOf(type) > -1) return true;
                return false;
            });
            if (!isTypeOk) {
                proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`)
                return false;
            }
        }
        // 校检文件大小
        if (props.fileSize) {
            const isLt = file.size / 1024 / 1024 < props.fileSize;
            if (!isLt) {
                proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
                return false;
            }
        }
        // 校检文件数量
        if (props.fileLimit) {
            if (fileList.value.length >= props.fileLimit) {
                proxy.$modal.msgError(`上传文件个数不能超过 ${props.fileLimit} 个!`)
                return false;
            }
        }
        return true;
    };

    const handleExceed = () => {
        proxy.$modal.msgError(`上传文件个数不能超过 ${props.fileLimit} 个!`)
    };

    const handleUploadError = (err) => {
        proxy.$modal.msgError("上传失败, 请重试")
    };

    const handleUploadSuccess = (res, file) => {
        proxy.$modal.msgSuccess("上传成功");
        fileList.value.push(file);
        emit("input", fileList.value);
    };

    const handleDelete = (index) => {
        fileList.value.splice(index, 1);
        emit("input", fileList.value);
    };

    const getFileName = (name) => {
        if (name.lastIndexOf("/") > -1) {
            return name.slice(name.lastIndexOf("/") + 1).toLowerCase();
        } else {
            return "";
        }
    };

    onMounted(() => {
        fileList.value = list.value;
    });

    defineExpose({
        clearFiles
    });

</script>

<style scoped lang="scss">
.upload-file-uploader {
    margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}
.el-upload__tip{}
</style>
