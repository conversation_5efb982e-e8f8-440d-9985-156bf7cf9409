<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)" :title="hasTitle(onlyOneChild.meta.title)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <svg-icon v-if="!(onlyOneChild.isCustom || item.isCustom)" :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"/>
          <svg v-if="onlyOneChild.isCustom || item.isCustom" class="icon-custom" aria-hidden="true">
            <use :xlink:href="`#${(onlyOneChild.sidebarClass || (item.sidebarClass))}`"></use>
          </svg>
          <template #title><span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.shortTitle||onlyOneChild.meta.title }}</span></template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" teleported>
      <template v-if="item.meta" #title>
          <svg-icon  v-if="!item.isCustom" :icon-class="item.meta && item.meta.icon" />
          <svg v-if="item.isCustom" class="icon-custom" aria-hidden="true">
              <use :xlink:href="`#${item.sidebarClass}`"></use>
          </svg>
          <span class="menu-title" :title="hasTitle(item.meta.title)">{{ item.meta.shortTitle||item.meta.title }}</span>
      </template>

      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import '@/assets/icons/svg/bdh-aviation-mission/iconfont.js'
import '@/assets/icons/svg/bdh-app-gathering-manage/iconfont.js'
import '@/assets/icons/svg/bdh-samic-confirm/iconfont.js'
import '@/assets/icons/svg/systemagriculturalmachineryv2/iconfont.js'
import '@/assets/icons/svg/bdh-trace/iconfont.js'
import '@/assets/icons/svg/systemlandcontract/iconfont.js'
import '@/assets/icons/svg/bdh-subsidy/iconfont.js'
import '@/assets/icons/svg/bdh-info-publish/iconfont.js'
import '@/assets/icons/svg/bdh-question-feedback/iconfont.js'
import '@/assets/icons/svg/bdh-agric-cost-analysis/iconfont.js'
import '@/assets/icons/svg/bdh-agric-invest/iconfont.js'
import '@/assets/icons/svg/sysforestresource/iconfont.js'
import '@/assets/icons/svg/sysanimalhusbandry/iconfont.js'
import '@/assets/icons/svg/sysagriculturalsituation/iconfont.js'
import '@/assets/icons/svg/syslandresource/iconfont.js'
import '@/assets/icons/svg/systemfinance/iconfont.js'
import '@/assets/icons/svg/bdh-group-report/iconfont.js'
import '@/assets/icons/svg/bdh-caf/iconfont.js'
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import { getNormalPath } from '@/utils/tx'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({});

function hasOneShowingChild(children = [], parent) {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter(item => {
    if (item.hidden) {
      return false
    } else {
      // Temp set(will be used if only has one showing child)
      onlyOneChild.value = item
      return true
    }
  })

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
};

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
  }

  return getNormalPath(props.basePath + '/' + routePath)
}

function hasTitle(title){
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>

<style>
.icon.bdh-front-group{
  margin-right: 16px;
}


.icon-custom {
    width: 1em;
    height: 1em;
    position: relative;
    fill: currentColor;
    vertical-align: -2px;
    margin-left: 20px;
    margin-right: 16px;
    fill: currentColor;
}

</style>
