<template>
  <div
      class="sidebar-logo-container"
      :class="{ 'collapse': collapse }"
      :style="{
      backgroundColor:
        sideTheme === 'theme-dark'
          ? variables.menuBackground
          : sideTheme === 'theme-blue'
            ? variables.menuBlueBackground
            : variables.menuLightBackground
    }"
  >
    <transition name="sidebarLogoFade">
      <div v-if="collapse" key="collapse" class="sidebar-logo-link" @click="goIndex">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1
            class="sidebar-title"
            :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.logoTitleColor
                : sideTheme === 'theme-blue'
                  ? variables.logoBlueTitleColor
                  : variables.logoLightTitleColor
          }"
        >
          {{ title }}
        </h1>
      </div>
      <div v-else key="expand" class="sidebar-logo-link" @click="goIndex">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <!--        <svg-icon icon-class="logo" class="logo-svg"></svg-icon>-->
        <h1
            class="sidebar-title"
            :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.logoTitleColor
                : sideTheme === 'theme-blue'
                  ? variables.logoBlueTitleColor
                  : variables.logoLightTitleColor
          }"
        >
          {{ title }}
        </h1>
      </div>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss'
import logo from '@/assets/logo/logo2.png'
import useSettingsStore from '@/store/modules/settings'
import SvgIcon from '@/components/SvgIcon/index.vue'
import appStore from '@/store/modules/app'
import {useRouter} from 'vue-router'
const router = useRouter()

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

const userStore = appStore()
const title = computed(() => userStore.title)
const settingsStore = useSettingsStore()
const sideTheme = computed(() => settingsStore.sideTheme)

function goIndex(){
    console.log(userStore.menu)
    router.push({path:userStore.menu?.path+'/page'+userStore.menu?.path+'/index'})
}

</script>

<style lang="scss" scoped>
.logo-svg {
  vertical-align: -9px;
  font-size: 20px;
  margin-right: 8px !important;
}

.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 40px;
      height: 18px;
      vertical-align: middle;
      margin-right: 2px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 15px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
