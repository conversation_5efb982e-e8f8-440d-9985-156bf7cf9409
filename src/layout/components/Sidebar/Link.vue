<template>
  <component :is="type" v-bind="linkProps()">
    <slot />
  </component>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import { useRoute } from 'vue-router'
const route = useRoute()
const props = defineProps({
  to: {
    type: [String, Object],
    required: true
  },
  title:{
    type: String,
      default: ''
  }
})

const isExt = computed(() => {
  return isExternal(props.to)
})

const type = computed(() => {
  if (route.fullPath.includes('systemlandcontract')) {
    if (isExt.value && props.title == '业务监测预警管理') {
      return 'a'
    }
    return 'router-link'
  }else{
    if (isExt.value) {
      return 'a'
    }
    return 'router-link'
  }
})

function linkProps() {
  if (isExt.value) {
    if (route.fullPath.includes('systemlandcontract')) {
      if(props.title == '业务监测预警管理'){
        return{
          href: `${import.meta.env.VITE_APP_SSO_API_PREFIX}/sso/login?callbackUrl=`+ props.to,
          target: '_blank',
          rel: 'noopener'
        }
      }else{
        return {
          to: `/systemlandcontract/viewPage/iFrame/iFrame?url=${props.to}`
        }
      }
    }else{
      return {
        href: props.to,
        target: '_blank',
        rel: 'noopener'
      }
    }
  }
  return {
    to: props.to
  }
}
</script>
