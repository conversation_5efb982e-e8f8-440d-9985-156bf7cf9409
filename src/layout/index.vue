<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
      <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="!sidebar.hide && !poweredByQiankun" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container" :style="mainContainerStyle">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar v-if="!poweredByQiankun" @setLayout="setLayout" />
        <!-- 作为 qiankun 子应用时，这里只能隐藏而不能直接销毁 -->
        <tags-view v-if="needTagsView" :style="{ display: poweredByQiankun ? 'none' : undefined }" />
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>


      <div class="aiContainer">
          <div class="aiIcon" @click="openAiDialog">
              <img
                  src="@/assets/ai/eye.gif"
                  alt="客服"
              />
              <div class="aiText">AI助理</div>
          </div>
          <!-- AI对话弹窗 -->
          <div
              v-if="aiDialogVisible"
              class="ai-dialog"
              :style="{ top: dialogTop + 'px', left: dialogLeft + 'px',height: dialogHeight  + 'px'}"
          >
              <div class="ai-dialog-header">
                  <span class="ai-dialog-title">北大荒AI助理</span>
                  <span class="ai-dialog-close" @click="closeAiDialog">×</span>
              </div>
              <div class="ai-dialog-content">
                  <iframe
                      :src="iframeSrc"
                      frameborder="0"
                      width="100%"
                      height="100%"
                  ></iframe>
              </div>
          </div>
      </div>


  </div>
</template>

<script setup>
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import { ElNotification } from 'element-plus'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from "@/store/modules/user"
const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const aiDialogVisible = ref(false)
const dialogTop = ref(0)
const dialogLeft = ref(window.innerWidth - 400)
const dialogHeight = ref(window.innerHeight)
const isDragging = ref(false)
const dragOffset = reactive({ x: 0, y: 0 })
const iframeSrc =  import.meta.env.VITE_APP_AI_URL+'?systemCode';

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

let subscription;
let poweredByQiankun = window.__POWERED_BY_QIANKUN__;
let mainContainerStyle = window.__POWERED_BY_QIANKUN__ ? {background: '#fff', marginLeft: 0} : {};
const { proxy } = getCurrentInstance();
import useMqtt from '@/views/systemagriculturalmachineryv2/utils/mqtt.js'
const { startMqtt, disconnect } = useMqtt()
onMounted(() => {
  subscription = window.bdhMicroMainEvents?.themeChange.subscribe((data) => {
    // 使用pinia做状态管理采用如下写法
    settingsStore.changeSetting({
        key: "theme",
        value: data.colorPrimary,
    })
  })
})

onUnmounted(() => {
  subscription.unsubscribe()
})

watch(() => device.value, () => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
})

watchEffect(() => {
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})
// createdMqtt()
function createdMqtt() {
  disconnect()
  const mqtt = import.meta.env.VITE_APP_MQTT
  // const mqtt = window.VITE_APP_MQTT
  console.log('import.meta.env',import.meta.env)
  console.log('window',window)
  startMqtt(mqtt, 'machinery', '1234qwer', [`agmachine/async/${useUserStore().currentOrgCode}/${useUserStore().staffId}`], null, (topic, data) => {
    if (data) {
      let { type, msg } = JSON.parse(data)
      proxy.$bus.$emit('mqtt', JSON.parse(data))
      if (type == "basicinfo_aminfo_exportExcelAsync") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      } else if (type == "basicinfo_aminfo_exportExcelWithQrCode") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      }
    }
  })
}
function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}



// 打开AI对话框
function openAiDialog() {
    aiDialogVisible.value = true
}

// 关闭AI对话框
function closeAiDialog() {
    aiDialogVisible.value = false
}

// 开始拖拽
function startDrag(event) {
    isDragging.value = true
    dragOffset.x = event.clientX - dialogLeft.value
    dragOffset.y = event.clientY - dialogTop.value

    document.addEventListener('mousemove', onDrag)
    document.addEventListener('mouseup', stopDrag)

    const headerElement = event.currentTarget
    headerElement.addEventListener('mouseleave', stopDrag)
}

// 拖拽中
function onDrag(event) {
    if (!isDragging.value) return

    // 计算新位置
    let newLeft = event.clientX - dragOffset.x
    let newTop = event.clientY - dragOffset.y

    // 获取弹窗尺寸
    const dialogWidth = 400
    const dialogHeight = 700

    // 限制在窗口范围内移动
    // 左边界限制
    if (newLeft < 0) {
        newLeft = 0
    }
    // 右边界限制
    if (newLeft > window.innerWidth - dialogWidth) {
        newLeft = window.innerWidth - dialogWidth
    }
    // 上边界限制
    if (newTop < 0) {
        newTop = 0
    }
    // 下边界限制
    if (newTop > window.innerHeight - dialogHeight) {
        newTop = window.innerHeight - dialogHeight
    }

    dialogLeft.value = newLeft
    dialogTop.value = newTop
}

// 停止拖拽
function stopDrag() {
    isDragging.value = false
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', stopDrag)
    if (event && event.currentTarget) {
        event.currentTarget.removeEventListener('mouseleave', stopDrag)
    }
}

</script>

<style lang="scss" scoped>
  @import "@/assets/styles/mixin.scss";
  @import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

  .aiContainer {
      .aiIcon {
          position: fixed;
          right: 20px;
          bottom: 20px;
          width: 52px;
          height: 46px;
          cursor: pointer;
          z-index: 1000;
          background: url("@/assets/ai/bg.png") no-repeat;
          background-size: cover;
          text-align: center;

          img {
              position: absolute;
              top: -20px;
              left: 6px;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          }
      }

      .aiText {
          margin-top: 25px;
          text-align: center;
          font-weight: 700;
          font-size: 12px;
          background: linear-gradient(94.58deg, #0D41B3 18.84%, #001045 109.98%);
          -webkit-background-clip: text; // Safari兼容
          background-clip: text;
          -webkit-text-fill-color: transparent; // Safari兼容
          color: transparent; // 兼容性设置
      }



      .ai-dialog {
          position: fixed;
          width: 400px;
          height: 700px;
          background: #fff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 2000;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .ai-dialog-header {
              height: 40px;
              background: linear-gradient(90deg, #D9ECFF 2.73%, #E9E2FF 121.82%),
              linear-gradient(180deg, rgba(255, 255, 255, 0) -4.7%, #FFFFFF 19.96%);
              color: white;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 15px;
              //cursor: move;
              user-select: none;
              //&:hover {
              //    background: #cee8ff;
              //}

              .ai-dialog-title {
                  font-weight: 700;
                  font-size: 16px;
                  color: #1D2129;

              }

              .ai-dialog-close {
                  font-size: 20px;
                  cursor: pointer;
                  font-weight: bold;
                  color: #C8CCD4;

                  &:hover {
                      color: #000000;
                  }
              }
          }

          .ai-dialog-content {
              flex: 1;
              overflow: hidden;
          }
      }
  }
</style>
