import {ElMessage} from "element-plus";
import {contractPrintLX, getcontractPrint} from "@/api/systemlandcontract/contract/import.js";
// import {contractfun} from "@/utils/cop.js";

const baseURL = window.VITE_APP_BASE_API
//获取树第一个节点
export function getInitTreeCode(treeList)
{
	let initArray=[]
	let temp = treeList
	if(!temp || temp.length<1) return null
	initArray.push(temp[0].orgCode)
/*	while(temp[0].children)
	{
		temp = temp[0].children
		initArray.push(temp[0].orgCode)
	}*/
	return initArray
}

//获取树第一个节点 Name
export function getInitTreeName(treeList)
{
	let initArray= ''
	let temp = treeList
	if(!temp || temp.length<1) return null
	initArray = temp[0].orgFullName
/*	while(temp[0].children)
	{
		temp = temp[0].children
		initArray.push(temp[0].orgCode)
	}*/
	return initArray
}

//获取树第一个节点级别 level
export function getInitTreeLevel(treeList)
{
	let initArray= ''
	let temp = treeList
	if(!temp || temp.length<1) return null
	initArray = temp[0].orgLevel
	/*	while(temp[0].children)
        {
            temp = temp[0].children
            initArray.push(temp[0].orgCode)
        }*/
	return initArray
}

// 日期格式化
export function parseTime(time, pattern) {
	if (arguments.length === 0 || !time) {
		return null
	}
	const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
	let date
	if (typeof time === 'object') {
		date = time
	} else {
		if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
			time = parseInt(time)
		} else if (typeof time === 'string') {
			time = time.replace(new RegExp(/-/gm), '/');
		}
		if ((typeof time === 'number') && (time.toString().length === 10)) {
			time = time * 1000
		}
		date = new Date(time)
	}
	const formatObj = {
		y: date.getFullYear(),
		m: date.getMonth() + 1,
		d: date.getDate(),
		h: date.getHours(),
		i: date.getMinutes(),
		s: date.getSeconds(),
		a: date.getDay()
	}

	const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
		let value = formatObj[key]
		// Note: getDay() returns 0 on Sunday
		if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
		if (result.length > 0 && value < 10) {
			value = '0' + value
		}
		return value || 0
	})
	return time_str
}

// 表单重置
export function resetForm(refName) {
	if (this.$refs[refName]) {
		this.$refs[refName].resetFields();
	}
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
	var search = params;
	search.params = {};
	if (null != dateRange && '' != dateRange) {
		if (typeof(propName) === "undefined") {
			search.params["beginTime"] = dateRange[0];
			search.params["endTime"] = dateRange[1];
		} else {
			search.params["begin" + propName] = dateRange[0];
			search.params["end" + propName] = dateRange[1];
		}
	}
	return search;
}

// 回显数据字典--种植作物专用
export function selectDictLabelForRaiseCrops(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].raiseCropsCd == ('' + value)) {
			actions.push(datas[key].raiseCropsNm);
			return true;
		}
	})
	return actions.join('');
}
// 回显数据字典--作业环节专用
export function selectDictLabelForLink(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].linkCode == ('' + value)) {
			actions.push(datas[key].linkName);
			return true;
		}
	})
	return actions.join('');
}

// 回显数据字典
export function selectDictLabel(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].code == ('' + value)) {
			actions.push(datas[key].name);
			return true;
		}
	})
	return actions.join('');
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
	var actions = [];
	var currentSeparator = undefined === separator ? "," : separator;
	var temp = (value || '').split(currentSeparator);
	Object.keys((value || '').split(currentSeparator)).some((val) => {
		Object.keys(datas).some((key) => {
			if (datas[key].code == ('' + temp[val])) {
				actions.push(datas[key].name + currentSeparator);
			}
		})
	})
	return actions.join('').substring(0, actions.join('').length - 1);
}

// 通用下载方法
export function download(fileName) {
	window.location.href = baseURL + "/common/download?fileName=" + encodeURI(fileName) + "&delete=" + true;
}

// 字符串格式化(%s )
export function sprintf(str) {
	var args = arguments, flag = true, i = 1;
	str = str.replace(/%s/g, function () {
		var arg = args[i++];
		if (typeof arg === 'undefined') {
			flag = false;
			return '';
		}
		return arg;
	});
	return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
	if (!str || str == "undefined" || str == "null") {
		return "";
	}
	return str;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
	id = id || 'id'
	parentId = parentId || 'parentId'
	children = children || 'children'
	rootId = rootId || Math.min.apply(Math, data.map(item => { return item[parentId] })) || 0
	//对源数据深度克隆
	const cloneData = JSON.parse(JSON.stringify(data))
	//循环所有项
	const treeData = cloneData.filter(father => {
		let branchArr = cloneData.filter(child => {
			//返回每一项的子级数组
			return father[id] === child[parentId]
		});
		branchArr.length > 0 ? father.children = branchArr : '';
		//返回第一层
		return father[parentId] === rootId;
	});
	return treeData != '' ? treeData : data;
}

export function isIdentityId(idNumber) {
  if(idNumber){
    // var patrn = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;//长度或格式校验
    var patrn = /(^\d{15}$)|(^\d{17}(\d|X)$)/;//长度或格式校验
    //地区校验
    var aCity = {
      11: "北京",
      12: "天津",
      13: "河北",
      14: "山西",
      15: "内蒙古",
      21: "辽宁",
      22: "吉林",
      23: "黑龙江",
      31: "上海",
      32: "江苏",
      33: "浙江",
      34: "安徽",
      35: "福建",
      36: "江西",
      37: "山东",
      41: "河南",
      42: "湖北",
      43: "湖南",
      44: "广东",
      45: "广西",
      46: "海南",
      50: "重庆",
      51: "四川",
      52: "贵州",
      53: "云南",
      54: "西藏",
      61: "陕西",
      62: "甘肃",
      63: "青海",
      64: "宁夏",
      65: "新疆",
      71: "台湾",
      81: "香港",
      82: "澳门",
      91: "国外"
    };
    // 出生日期验证
    var sBirthday = (
        idNumber.substr(6, 4) +
        "-" +
        Number(idNumber.substr(10, 2)) +
        "-" +
        Number(idNumber.substr(12, 2))
      ).replace(/-/g, "/"),
      d = new Date(sBirthday)
    // 身份证号码校验 最后4位  包括最后一位的数字/字母X
    var sum = 0,
      weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
      codes = "10X98765432";
    for (var i = 0; i < idNumber.length - 1; i++) {
      sum += idNumber[i] * weights[i];
    }
    var last = codes[sum % 11]; //计算出来的最后一位身份证号码

    var errorMsg = '';
    if (!patrn.exec(idNumber)) {
      errorMsg = "你输入的身份证长度或格式错误"
    } else if (!aCity[parseInt(idNumber.substr(0, 2))]) {
      errorMsg = "你的身份证地区非法"
    } else if (sBirthday != d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate()) { errorMsg = "身份证上的出生日期非法" } else if (idNumber[idNumber.length - 1] != last) {
      errorMsg = "你输入的身份证号非法"
    } else{
      errorMsg = ''
    }
    return errorMsg;
  }

}

// 修改按钮样式
export function updateButtonStyle() {
	if(document.querySelector('.mb8') != null) {
		let nodes = document.querySelector('.mb8').childNodes;
		let index = 0;
		for (let j = 0; j < nodes.length; j++) {
			let child = nodes[j].childNodes
			if (child.length > 0 && nodes[j].querySelector('button')!=null) {
				nodes[j].querySelector('button').style.color = '#606266'
				nodes[j].querySelector('button').style.backgroundColor = '#FFFFFF'
				nodes[j].querySelector('button').style.borderColor = '#DCDFE6'
			}
		}
		for (let i = 0; i < nodes.length; i++) {
			let children = nodes[i].childNodes
			if (children.length > 0 && nodes[i].querySelector('button')!=null) {
				index = i
				break
			}
		}
		nodes[index].querySelector('button').style.color = '#FFFFFF'
		nodes[index].querySelector('button').style.backgroundColor = '#006be1'
		nodes[index].querySelector('button').style.borderColor = '#006be1'
	}
}

// 公共序号编号查询
export async function queryCodefun() {
	let res = {}
	await queryCode().then(response => {
		res = response.data
	});
	return res
}
//根据身份证算年龄
export function getAge(identify) {
	var UUserCard = identify;
	if (UUserCard != null && UUserCard != '') {
		var myDate = new Date();
		var month = myDate.getMonth() + 1;
		var day = myDate.getDate();
		var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1;
		if (UUserCard.substring(10, 12) < month || UUserCard.substring(10, 12) == month && UUserCard.substring(12, 14) <= day) {
			age++;
		}
		return age;
	}
}

//敏感数据掩码，参数：原文，从startNum到endNum-1打*
//如mask("abcde",2,4) = cd
export function mask(name, startNum, endNum) {
	if (name) {
		if (startNum < name.length && endNum <= name.length && startNum <= endNum)
			var data = endNum - startNum
		var datax = ""
		for (var i = 0; i < data; i++) {
			datax += '*'
		}
		var shortName = name.substring(0, startNum - 1) + datax + name.substring(endNum - 1, name.length)
		return shortName

	} else {
		return ''
	}
}

/**
 @name:
 @description:
 @author: qikai
 @time: 2021-12-18 10:41:25
 **/
const checkIdNum = (rule, value, callback) => {
	const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
	if (!value) {
		return callback(new Error('证件号码不能为空'))
	} else if (!reg.test(value)) {
		return callback(new Error('证件号码不正确'))
	} else {
		callback()
	}
};
//大于0浮点数
export const checkIsFloat = (rule, value, callback) => {
	const patrn = /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/;//数字校验
	// const patrn = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/
	var errorMsg = '';
	if(value){
		if (!patrn.exec(value)) {
			errorMsg = "请输入大于0的数字"
		}
	}
	if (errorMsg) {
		callback(new Error(errorMsg));
	} else {
		callback();
	}
};
//正浮点数
export const checkFloat = (rule, value, callback) => {
	const patrn = /^(0|([1-9]\d*))(\.\d+)?$/g;
	var errorMsg = '';
	if(value){
		if (!patrn.exec(value)) {
			errorMsg = "请输入大于等于0的数字"
		}
	}

	if (errorMsg) {
		callback(new Error(errorMsg));
	} else {
		callback();
	}
};
//整数
export const checkIsNumber = (rule, value, callback) => {
	const patrn = /(^[0-9]*$)/;//数字校验
	var errorMsg = '';
	if(value){
		if (!patrn.exec(value)) {
			errorMsg = "请输入整数"
		}
	}

	if (errorMsg) {
		callback(new Error(errorMsg));
	} else {
		callback();
	}
};
//大于0的整数
export const checkNumber = (rule, value, callback) => {
	const patrn = /(^[0-9]*$)/;//数字校验
	var errorMsg = '';
	if(value){
		if (!patrn.exec(value)) {
			errorMsg = "请输入整数"
		}
		if(value == 0){
			errorMsg = "请输入大于0的整数"
		}
	}

	if (errorMsg) {
		callback(new Error(errorMsg));
	} else {
		callback();
	}
};
//循环审核标志
export function Levelfun(maxAuditLevel){
	const englishdata = ['A','B','C','D','E','F','G','H']
	const bighdata = ['一','二','三','四','五','六','七','八']
	let tabledata = []
	if(maxAuditLevel>0 && maxAuditLevel){
		for(let i = 0; i < maxAuditLevel;i++){
			tabledata.push({
				name: `audit${englishdata[i]}Flag`,
				label: `${bighdata[i]}级审核状态`,
				formatter:`approval${englishdata[i]}Format`
			},{
				name: `audit${englishdata[i]}Name`,
				label: `${bighdata[i]}级审核人姓名`
			},{
				name: `audit${englishdata[i]}Time`,
				label: `${bighdata[i]}级审核时间`
			})
		}
	}
	return tabledata;
}
//不能含有特殊字符
export const validateInput = (rule,value,callback) => {
	if (!checkSpecialKey(value)) {
		callback(new Error('不能含有特殊字符！'))
	} else {
		callback()
	}
};
//特殊字符判断
export function checkSpecialKey(str) {
	const specialKey = "[`~!#$^&*()=|{}':;'\\[\\]<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]‘'";
	for (var i = 0; i < str.length; i++) {
		if (specialKey.indexOf(str.substr(i, 1)) != -1) {
			return false;
		}
	}
	return true;
};

//千分符 不控制小数
export function numFormat(num){
	if(num !=undefined && num !=null && num !=''){
		num=num.toString().split(".");  // 分隔小数点
		var arr=num[0].split("").reverse();  // 转换成字符数组并且倒序排列
		var res=[];
		for(var i=0,len=arr.length;i<len;i++){
			if(i%3===0&&i!==0){
				res.push(",");   // 添加分隔符
			}
			res.push(arr[i]);
		}
		res.reverse(); // 再次倒序成为正确的顺序
		if(num[1]){  // 如果有小数的话添加小数部分
			res=res.join("").concat("."+num[1]);
		}else{
			res=res.join("");
		}
		return res;
	}else{
		return '0.00'
	}

}

var pringLoading = null
var count = 0
var timer = null
// ischeck 是否不验证是否签订过
//批量打印
export function pdfDownBatchfun(copyParams,ids,fddContractNo,contractType,ischeck){
	if(ischeck){
		if(copyParams.length != ids.length){
			ElMessage.info('只允许打印已签订合同');
			return false;
		}
	}else{
		if(fddContractNo.includes('') || fddContractNo.includes(null)|| fddContractNo.includes(undefined)){
			ElMessage.info('请先生成合同');
			return false;
		}
	}
	let uuid = uuidv4().replace(/-/g,'')
	let params = {
		"fddContractNoList": Array.from(new Set(fddContractNo)),
		contractType,
		"mobileDevice":false,//非移动端
		"mergeFddContractNo": uuid,
		"resType":"firstRes"
	}
	getcontractPrint(params).then((res) => {
		if(res.code === 0){
			if(res.data && res.data.includes('http')){
				window.open(res.data, '_blank')
			}
			if( res.msg == 'waitFddReq'  ){
				pringLoading = this.$loading({
					lock: true,
					text: '加载中……',
					background: 'rgba(0, 0, 0, 0.2)'
				});
				count = 0
				contractfun(uuid,Array.from(new Set(fddContractNo)),contractType)
			}
		}else{
			ElMessage.info(res.msg);
		}
	})
}


//批量打印轮询
export function contractfun(uuid,fddContractNoList,contractType){
	//就是第一次与第二次请求之间按照合同数量计算下，每条算3秒吧。然后第三次开始每隔3秒
	// let times =(3000* (this.fddContractNo.length + 1)) + (this.count * 3000)
	//轮询
	timer = setInterval(()=>{
		let params = {
			fddContractNoList,
			contractType,
			"mobileDevice":false,//非移动端
			mergeFddContractNo:uuid,
			"resType":'waitFddReq'
		}
		contractPrintLX(params).then(status=>{
			count++
			if(status.data  && status.data.includes('http')){
				pringLoading.close()
				clearInterval(timer)
				window.open(status.data, '_blank')

			}else if(count>=100){
				ElMessage.info("打印操作已超时");
				pringLoading.close()
				clearInterval(timer)
			}
		}).catch(err=>{
			pringLoading.close()
			clearInterval(timer)
			console.log(err)

		})
	},3000)
}
