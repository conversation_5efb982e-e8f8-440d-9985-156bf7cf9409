import request from '@/utils/request'
const env = import.meta.env

// 查询退回列表
export function listRefund(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/queryByPage`,
    method: 'post',
    data
  })
}

// 查询已发放退回明细
export function getRefundDetail(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/queryPaymentItemByPage`,
    method: 'post',
     data
  })
}

// 新增 退回
export function addRefundInsert(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/insert`,
    method: 'post',
    data
  })
}


// 删除退回
export function delRefund(subsidyAgmachineDetailId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/logicDeleteById/` + subsidyAgmachineDetailId,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delRefunds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/logicDeleteByIds`,
    method: 'post',
    data
  })
}
// 审核
export function auditPass(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/auditPassByIds`,
    method: 'post',
    data
  })
}

// 取消审核
export function auditCancel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/auditCancelByIds`,
    method: 'post',
    data
  })
}
// 补充审核
export function refunAdddFile(data){
  return request({
    url:`${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/addFile`,
    method:'post',
    data
  })
}


// 退回详情
export function refundInfo(subsidyAgmachineDetailId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/info/` + subsidyAgmachineDetailId,
    method: 'post'
  })
}

// 全部审核
export function auditPassAll(data){
  return request({
    url:`${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyRefund/auditPassAll`,
    method:'post',
    data
  })
}